{"questions": [{"question_number": 1, "question_text": "Question 1: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 2, "question_text": "Question 2: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 3, "question_text": "Question 3: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 4, "question_text": "Question 4: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 5, "question_text": "Question 5: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 6, "question_text": "Question 6: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 7, "question_text": "Question 7: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 8, "question_text": "Question 8: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 9, "question_text": "Question 9: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 10, "question_text": "Question 10: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 11, "question_text": "Question 11: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 12, "question_text": "Question 12: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 13, "question_text": "Question 13: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 14, "question_text": "Question 14: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 15, "question_text": "Question 15: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 16, "question_text": "Question 16: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 17, "question_text": "Question 17: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 18, "question_text": "Question 18: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 19, "question_text": "Question 19: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}, {"question_number": 20, "question_text": "Question 20: Technical question about data architecture concepts?", "expected_response": {"junior_answer": "Basic understanding of the concept with minimal detail.", "mid_answer": "Good understanding with some practical examples and moderate depth.", "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."}}]}