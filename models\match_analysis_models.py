from datetime import datetime
from typing import Any, Dict, Optional, List

from pydantic import BaseModel, Field, ConfigDict
from typing_extensions import Annotated


class CandidatePositionAnalysis(BaseModel):
    LLM_Analysis: Dict[str, Any] = Field(
        ..., description="Analysis results from the LLM"
    )
    extra_questions: Dict[str, Any] = Field(
        ..., description="Additional clarifications or interview questions suggested"
    )
    highlights: Dict[str, Any] = Field(
        ...,
        description="Key points or standout features of the candidate for this position",
    )
    Score: float = Field(..., description="Overall score assigned by the LLM.")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


# class SkillMatchAnalysis(BaseModel):
#     coef_match: float = Field(..., description="Coefficient indicating the degree of skill match.")
#     comment: str = Field(..., description="Commentary on the skill match.")
#     model_config = ConfigDict(populate_by_name=True) 

class LLMAnalysis(BaseModel):
    reason: str = Field(..., description="Explanation of the analysis.")
    skill_match_analysis: Dict[str, str] = Field(
        ..., description="Analysis of skill matches, keyed by skill name. You can just cite matched skills, not all skills. You can not repeat the not matched skills."
    )
    skill_not_matched: List[str] = Field(..., description="List of skills not matched. You can not repeat the matched skills.")

    model_config = ConfigDict(extra="allow")


class PositionCandidateAnalysis(BaseModel):
    LLM_Analysis: LLMAnalysis = Field(..., description="Analysis provided by the LLM.")
    extra_questions: List[str] = Field(
        ..., description="Additional questions generated by the LLM."
    )
    highlights: List[str] = Field(
        ..., description="Key points highlighted in the analysis."
    )
    Score: float = Field(..., description="Overall score assigned by the LLM. This is a value between 0 and 100.")


class BatchMatchAnalysis(BaseModel):
    candidates_analysis: List[PositionCandidateAnalysis] = Field(
        ..., description="List of analysis results for multiple candidates"
    )
# summary: Dict[str, Any] = Field(
#     ..., description="Overall summary of the batch analysis"
# )
# created_at: datetime = Field(default_factory=datetime.now)
# updated_at: datetime = Field(default_factory=datetime.now)


class CompatibilityEvaluation(BaseModel):
    compatibilityPercentage: float = Field(..., description="Percentage of compatibility with the position (based on skills, experience, education, and other key requirements). This is a percentage between 0.0 and 100.0. Example: 85.5, 45.1, 99.0")
    recommendation: bool = Field(..., description="Recommendation: Indicate whether or not the candidate is recommended to move forward in the process, along with a brief justification.")
    justification: str = Field(..., description="Justification for the recommendation")
    matchesFound: List[str] = Field(..., description="Matches Found: List in a clear and orderly manner the points on which the candidate meets the requirements in the Job Description.")
    missing: List[str] = Field(..., description="Missing: Indicate the key requirements or competencies from the Job Description that do not appear or are not evident in the candidate's CV.")


# Experience-based reranking models
class WorkExperience(BaseModel):
    """Individual work experience entry extracted from candidate CV"""
    role_title: str = Field(..., description="Job title or role name")
    company: str = Field(..., description="Company or organization name")
    duration_years: float = Field(..., description="Duration of experience in years (e.g., 2.5 for 2 years 6 months)")
    key_responsibilities: List[str] = Field(..., description="Key responsibilities and achievements in this role")
    technologies_used: List[str] = Field(..., description="Technologies, tools, and platforms used in this role")
    industry_domain: Optional[str] = Field(None, description="Industry or business domain (e.g., Healthcare, Finance, E-commerce)")


class ExperienceAnalysis(BaseModel):
    """Structured analysis of candidate's work experience"""
    total_years_experience: float = Field(..., description="Total years of professional experience")
    relevant_experiences: List[WorkExperience] = Field(..., description="List of work experiences relevant to the target position")
    primary_role_types: List[str] = Field(..., description="Primary role types the candidate has experience in (e.g., QA Engineer, Data Architect, Software Developer)")
    domain_expertise: List[str] = Field(..., description="Domains or industries where the candidate has significant experience")
    technical_progression: str = Field(..., description="Summary of how the candidate's technical skills have progressed over time")


class ExperienceRelevanceScore(BaseModel):
    """Score indicating how relevant candidate's experience is to a specific position"""
    overall_relevance_score: float = Field(..., description="Overall relevance score from 0.0 to 100.0")
    role_type_match_score: float = Field(..., description="How well candidate's role types match the target position (0.0 to 100.0)")
    technology_match_score: float = Field(..., description="How well candidate's technology experience matches position requirements (0.0 to 100.0)")
    domain_match_score: float = Field(..., description="How well candidate's domain experience matches position context (0.0 to 100.0)")
    seniority_match_score: float = Field(..., description="How well candidate's experience level matches position seniority (0.0 to 100.0)")
    relevance_explanation: str = Field(..., description="Detailed explanation of why this experience is relevant or not relevant to the position")
    key_experience_highlights: List[str] = Field(..., description="Most relevant experience highlights for this specific position")


class CandidateExperienceRanking(BaseModel):
    """Candidate information with experience-based ranking data"""
    candidate_id: str = Field(..., description="Unique identifier for the candidate")
    original_similarity_score: float = Field(..., description="Original vector similarity score")
    experience_relevance_score: float = Field(..., description="Experience-based relevance score (0.0 to 100.0)")
    combined_score: float = Field(..., description="Combined score incorporating both similarity and experience relevance")
    experience_analysis: ExperienceAnalysis = Field(..., description="Detailed experience analysis for the candidate")
    relevance_scoring: ExperienceRelevanceScore = Field(..., description="Relevance scoring against the target position")
    ranking_explanation: str = Field(..., description="Explanation of why this candidate was ranked at this position")


class BatchExperienceReranking(BaseModel):
    """Results of experience-based reranking for multiple candidates"""
    reranked_candidates: List[CandidateExperienceRanking] = Field(..., description="Candidates reordered by experience relevance")
    reranking_summary: str = Field(..., description="Summary of the reranking process and key insights")
    position_title: str = Field(..., description="Title of the position candidates were ranked against")
    total_candidates_processed: int = Field(..., description="Total number of candidates that were reranked")
