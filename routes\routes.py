from datetime import datetime
import os
from typing import List
import psycopg2
from fastapi import APIRouter, HTTPException, Query
# from core.config import settings
# from utils.match_evaluations import evaluate_candidate, evaluate_position
from opentelemetry import trace  # NEW
from models.match_analysis_models import CompatibilityEvaluation
from controllers.match_controller import get_custom_match, get_matching_results_by_position, get_matching_results_by_candidate

from utils import match_evaluations as match_functions 
# Telemetry Section
import logging
import concurrent.futures
from functools import partial
from core.config import settings
from config.config import MODELS_CONFIG
from contextlib import contextmanager
import time
from models.llm import models_pool
from controllers.candidates_controller import fetch_all_candidates, get_candidates_by_ids
from controllers.positions_controller import fetch_all_positions, get_position_by_id
# Configurar el logger de Python (los logs se enviarán a App Insights por OpenTelemetry)
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__) 
router = APIRouter()
from templates.candidates_templates.candidate_analysis import templatesObject

threshold_match = os.getenv("MAXIMUM_NUMBER_OF_MATCHES", 50)


@contextmanager
def get_cursor():
    conn = psycopg2.connect(settings.DATABASE_URL)
    try:
        with conn:
            with conn.cursor() as cur:
                yield cur
    finally:
        conn.close()


@router.get("/test_llm_match")
def test_llm_match(model_name: str, position_id: str, candidate_id: str = None):  # Candidate ID is optional
    """
    Endpoint to test LLM output for a single candidate match.
    """
    try:
        templatesObject.update_prompts()
        from controllers.candidates_controller import test_llm_match_logic
        return test_llm_match_logic(model_name, position_id, candidate_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/test_llm_match_with_skill_priority")
def test_llm_match_with_skill_priority(model_name: str, position_id: str, candidate_id: str = None):
    """
    Enhanced endpoint to test and compare regular vs skill-aware matching.
    """
    try:
        from controllers.candidates_controller import test_llm_match_logic_with_skill_priority
        return test_llm_match_logic_with_skill_priority(model_name, position_id, candidate_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/get_current_models")
def get_current_models():
    """
    Endpoint to get the current models available in the pool.
    """
    try:
        # Return the keys and its values from the models_pool
        models_info = {name: model.model_dump() for name, model in models_pool.items()}
        return models_info
    except Exception as e:
        logger.error(f"Error retrieving models: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving models")


# Check LLM health status
@router.get('/check_llm_health')
def check_llm_health():
    logger.info("Checking LLM health status")
    try:
        response1 = models_pool["llama4-pro"].invoke("1+1?").content
        logger.info("LLM llama is healthy")
        response2 = models_pool["gpt-4o-mini"].invoke("1+1?").content
        logger.info("LLM is gpt is healthy")

        response = f"LLM llama is healthy: {response1}, LLM gpt is healthy: {response2}"
        return {"status": "Healthy", "response": response}
    except HTTPException as http_exc:
        logger.error(f"LLM health check failed: {http_exc.detail}")
        return {"status": "Unhealthy", "error": http_exc.detail}
    except Exception as e:
        logger.error(f"LLM health check failed: {str(e)}")
        return {"status": "Unhealthy", "error": str(e)}


# Context manager to log time taken for a block of code
@contextmanager
def log_time_block(block_name):
    start_time = time.perf_counter()
    yield
    end_time = time.perf_counter()
    elapsed_time = end_time - start_time
    logger.info(f"Block '{block_name}' executed in {elapsed_time:.4f} seconds")


# This endpoint matches a position with candidates or a candidate with positions.
@router.post("/match")
def execute_match(
    position_id: str = Query(None), 
    candidate_id: str = Query(None),
    limit: int = 5,
    hasFeedback: int = Query(2, description="If 0, without feedback. If 1, with feedback. If 2, both."),
    batch_mode: bool = Query(True, description="If True, analyze all candidates in one prompt. If False, analyze in parallel.")
):
    try:
        """
        Endpoint to match a position with candidates or a candidate with positions.
        - If position_id is provided, it retrieves candidates for that position.
        - If candidate_id is provided, it retrieves positions for that candidate.
        - If both are provided, it prioritizes position_id.
        Supports batch (single prompt) or parallel processing modes.
        """
        logger.info("Starting endpoint /match with position_id=%s candidate_id=%s limit=%d batch_mode=%s",
                    position_id, candidate_id, limit, batch_mode)

        with tracer.start_as_current_span("execute_match_logic") as span:
            if not position_id and not candidate_id:
                logger.warning("Neither position_id nor candidate_id provided")
                raise HTTPException(status_code=400, detail="You must provide either position_id or candidate_id.")

            # Connect to the database
            # Add a sub-span for the query
            with tracer.start_as_current_span("db_connect_and_query") as db_span:
                db_span.set_attribute("database.url", settings.DATABASE_URL)
                if position_id:
                    # Retrieve position embedding & text
                    logger.info("Retrieved embedding for position Id=%s", position_id)
                    position_embedding, processed_position = _get_position_embedding_and_text(position_id)
                    # Search for candidates 
                    results = _search_candidates_for_position_embedding(
                        position_id, position_embedding, hasFeedback, limit
                    )
                else:
                    # Retrieve candidate embedding & text
                    logger.info("Retrieved embedding for candidate Id=%s", candidate_id)
                    candidate_embedding, processed_candidate = _get_candidate_embedding_and_text(candidate_id)
                    # Search for positions
                    results = _search_positions_for_candidate_embedding(
                        candidate_id, candidate_embedding, limit
                    )

            # We process the results (another sub-span, for example, for matching logic with LLM)
            with tracer.start_as_current_span("matching_evaluation") as eval_span:
                if position_id:
                    eval_span.set_attribute("match.position_id", position_id)
                    if batch_mode:
                        return _batch_mode_analysis(results, processed_position)
                    if not batch_mode:  # Either by choice or fallback
                        return _process_candidates_parallel(results, processed_position)
                else:
                    eval_span.set_attribute("match.candidate_id", candidate_id)
                    positions_result = []
                    for r in results:
                        pos_id_db = r[0]
                        proj_id_db = r[1]
                        position_info_db = r[2]
                        similarity_score = r[3]
                        position_text = r[4]

                        # Use skill-aware matching for position evaluation
                        try:
                            analysis = match_functions.evaluate_candidate_with_skill_priority(
                                processed_candidate, position_text
                            )
                        except Exception as e:
                            # Fallback to old matching if skill-aware fails
                            logger.warning(f"Skill-aware position matching failed, falling back to old matching: {e}")
                            analysis = match_functions.evaluate_position(
                                processed_candidate, position_text
                            )

                        positions_result.append({
                            "id": pos_id_db,
                            "proj_id": proj_id_db,
                            "position_info": position_info_db,
                            "cosine_similarity": similarity_score,
                            "analysis": dict(analysis),
                        })

                    final_match_response = {
                        "matched_positions": positions_result,
                        "processed_candidate": processed_candidate,
                        "timestamp": datetime.now(),
                    }
                    logger.info("Returning %d matched positions", len(positions_result))
                    return final_match_response
    except psycopg2.Error as db_error:
        logger.error("Database error: %s", db_error, exc_info=True)
        raise HTTPException(status_code=500, detail=f"execute_match. Database error occurred: {str(db_error)}")
    except HTTPException as http_exc:
        logger.error("HTTP Exception in endpoint /match: %s", http_exc.detail, exc_info=True)
        raise http_exc
    except Exception as e:
        logger.error("Error occurred : %s", e, exc_info=True)
        raise HTTPException(status_code=500, detail="An error occurred while processing the match")


# Helper function for parallel processing
def process_candidate_parallel(result_row, processed_position):
    """
    Processes a single candidate row in parallel, evaluating compatibility with the given position.
    """
    candidate_id_db = result_row[0]
    proj_id_db = result_row[1]
    candidate_info_db = result_row[2]
    similarity_score = result_row[3]
    candidate_text = result_row[4]

    # Process this candidate using skill-aware matching
    try:
        analysis = match_functions.evaluate_candidate_with_skill_priority(
            candidate_text,
            processed_position
        )
    except Exception as e:
        # Fallback to old matching if skill-aware fails
        logger.warning(f"Skill-aware matching failed, falling back to old matching: {e}")
        analysis = match_functions.evaluate_candidate(
            candidate_text,
            processed_position
        )

    return {
        "id": candidate_id_db,
        "proj_id": proj_id_db,
        "candidate_info": candidate_info_db,
        "cosine_similarity": similarity_score,
        "analysis": dict(analysis),
    }


# Custom prompt evaluation
# This endpoint evaluates a candidate's CV against a job description using a custom prompt.
@router.post("/evaluate_candidate_custom_prompt")
def evaluate_candidate_custom_prompt(candidate_text: str, processed_position: str) -> CompatibilityEvaluation:
    """
    Evaluates a candidate's CV against a job description using a custom prompt.
    Returns a structured analysis including compatibility percentage, recommendation, matches found, and missing requirements.
    """
    logger.info("Evaluating candidate with custom prompt")
    if not candidate_text or not processed_position:
        logger.error("Candidate text or processed position is empty")
        raise HTTPException(status_code=400, detail="Candidate text and processed position are required")
    # Use skill-aware matching for custom prompt evaluation
    try:
        analysis = match_functions.get_candidate_analysis_with_skill_priority(
            candidate_text=candidate_text,
            processed_position=processed_position
        )
    except Exception as e:
        # Fallback to old matching if skill-aware fails
        logger.warning(f"Skill-aware custom prompt matching failed, falling back to old matching: {e}")
        analysis = match_functions.get_candidate_analysis_custom_prompt(
            candidate_text=candidate_text,
            processed_position=processed_position
        )
    if not analysis:
        logger.error("No analysis result returned from get_candidate_analysis_custom_prompt")
        raise HTTPException(status_code=404, detail="No analysis result available for the provided candidate and position")

    return analysis


# Skill-aware evaluation endpoints
@router.post("/evaluate_candidate_with_skill_priority")
def evaluate_candidate_with_skill_priority(candidate_text: str, position_text: str) -> dict:
    """
    Enhanced candidate evaluation that first extracts prioritized skills from the position,
    then performs skill-aware matching.

    Args:
        candidate_text: Candidate description/CV text
        position_text: Position description text

    Returns:
        dict: Analysis response with skill-aware evaluation including position skills analysis
    """
    logger.info("Evaluating candidate with skill priority analysis")
    if not candidate_text or not position_text:
        logger.error("Candidate text or position text is empty")
        raise HTTPException(status_code=400, detail="Candidate text and position text are required")

    analysis = match_functions.evaluate_candidate_with_skill_priority(
        candidate=candidate_text,
        position=position_text
    )

    if not analysis:
        logger.error("No analysis result returned from evaluate_candidate_with_skill_priority")
        raise HTTPException(status_code=500, detail="Skill-aware analysis failed")

    return analysis


@router.post("/evaluate_candidate_custom_prompt_with_skill_priority")
def evaluate_candidate_custom_prompt_with_skill_priority(candidate_text: str, processed_position: str) -> CompatibilityEvaluation:
    """
    Enhanced custom prompt evaluation that first extracts prioritized skills from the position,
    then performs skill-aware compatibility evaluation.

    Args:
        candidate_text: Candidate's CV/resume text
        processed_position: Processed position description

    Returns:
        CompatibilityEvaluation: Enhanced compatibility evaluation with skill prioritization
    """
    logger.info("Evaluating candidate with skill-aware custom prompt")
    if not candidate_text or not processed_position:
        logger.error("Candidate text or processed position is empty")
        raise HTTPException(status_code=400, detail="Candidate text and processed position are required")

    analysis = match_functions.get_candidate_analysis_with_skill_priority(
        candidate_text=candidate_text,
        processed_position=processed_position
    )

    if not analysis:
        logger.error("No analysis result returned from get_candidate_analysis_with_skill_priority")
        raise HTTPException(status_code=500, detail="Skill-aware custom analysis failed")

    return analysis


@router.post("/test_experience_reranking")
def test_experience_reranking(
    candidate_texts: List[str],
    position_text: str,
    experience_weight: float = 0.6,
    similarity_weight: float = 0.4
):
    """
    Test endpoint for experience-based reranking functionality.

    This endpoint allows testing the reranking system with custom candidate texts
    and position descriptions without requiring database lookups.

    Args:
        candidate_texts: List of candidate CV/resume texts
        position_text: Position description text
        experience_weight: Weight for experience relevance (default: 0.6)
        similarity_weight: Weight for similarity score (default: 0.4)

    Returns:
        dict: Reranking results with detailed analysis
    """
    logger.info("Testing experience-based reranking with %d candidates", len(candidate_texts))

    if not candidate_texts:
        raise HTTPException(status_code=400, detail="At least one candidate text is required")

    if not position_text or not position_text.strip():
        raise HTTPException(status_code=400, detail="Position text is required")

    try:
        # Create mock results structure similar to database results
        # Format: [candidate_id, proj_id, candidate_info, similarity_score, candidate_text]
        mock_results = []
        for i, candidate_text in enumerate(candidate_texts):
            mock_results.append([
                f"test_candidate_{i + 1}",  # candidate_id
                "test_project",           # proj_id
                {"name": f"Test Candidate {i + 1}"},  # candidate_info
                0.8 - (i * 0.1),         # decreasing similarity scores
                candidate_text            # candidate_text
            ])

        # Apply reranking
        reranked_results = match_functions.rerank_candidates_by_experience(
            results=mock_results,
            processed_position=position_text,
            experience_weight=experience_weight,
            similarity_weight=similarity_weight
        )

        # Format response with detailed analysis
        response = {
            "original_order": [
                {
                    "candidate_id": result[0],
                    "similarity_score": result[3],
                    "candidate_preview": result[4][:200] + "..." if len(result[4]) > 200 else result[4]
                }
                for result in mock_results
            ],
            "reranked_order": [
                {
                    "candidate_id": result[0],
                    "similarity_score": result[3],
                    "candidate_preview": result[4][:200] + "..." if len(result[4]) > 200 else result[4]
                }
                for result in reranked_results
            ],
            "reranking_summary": {
                "total_candidates": len(candidate_texts),
                "experience_weight": experience_weight,
                "similarity_weight": similarity_weight,
                "order_changed": [r[0] for r in mock_results] != [r[0] for r in reranked_results]
            },
            "position_preview": position_text[:300] + "..." if len(position_text) > 300 else position_text,
            "timestamp": datetime.now()
        }

        logger.info("Experience reranking test completed successfully")
        return response

    except Exception as e:
        logger.error("Error in experience reranking test: %s", str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Reranking test failed: {str(e)}")


@router.post("/test_experience_extraction")
def test_experience_extraction(candidate_text: str):
    """
    Test endpoint for experience extraction functionality.

    This endpoint allows testing the experience extraction system with custom candidate text.

    Args:
        candidate_text: Candidate CV/resume text

    Returns:
        dict: Extracted experience analysis
    """
    logger.info("Testing experience extraction")

    if not candidate_text or not candidate_text.strip():
        raise HTTPException(status_code=400, detail="Candidate text is required")

    try:
        # Extract experience using the reranking function
        experience_analysis = match_functions.extract_candidate_experience(candidate_text)

        if not experience_analysis:
            raise HTTPException(status_code=500, detail="Experience extraction failed")

        # Format response
        response = {
            "extraction_successful": True,
            "total_years_experience": experience_analysis.total_years_experience,
            "primary_role_types": experience_analysis.primary_role_types,
            "domain_expertise": experience_analysis.domain_expertise,
            "technical_progression": experience_analysis.technical_progression,
            "relevant_experiences": [
                {
                    "role_title": exp.role_title,
                    "company": exp.company,
                    "duration_years": exp.duration_years,
                    "key_responsibilities": exp.key_responsibilities,
                    "technologies_used": exp.technologies_used,
                    "industry_domain": exp.industry_domain
                }
                for exp in experience_analysis.relevant_experiences
            ],
            "candidate_preview": candidate_text[:300] + "..." if len(candidate_text) > 300 else candidate_text,
            "timestamp": datetime.now()
        }

        logger.info("Experience extraction test completed successfully")
        return response

    except Exception as e:
        logger.error("Error in experience extraction test: %s", str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Experience extraction test failed: {str(e)}")


@router.post("/extract_position_skills")
def extract_position_skills(position_text: str):
    """
    Extract and prioritize skills from a job position for matching purposes.

    Args:
        position_text: Position description text to analyze

    Returns:
        dict: Structured analysis with prioritized skills and weights
    """
    logger.info("Extracting prioritized skills from position")
    if not position_text:
        logger.error("Position text is empty")
        raise HTTPException(status_code=400, detail="Position text is required")

    skills_analysis = match_functions.extract_position_skills_for_matching(position_text)

    if not skills_analysis:
        logger.error("No skills analysis result returned")
        raise HTTPException(status_code=500, detail="Skills extraction failed")

    return skills_analysis.model_dump()


# Helper function to get the candidate query based on feedback status
# This function returns a SQL query string based on the hasFeedback parameter.
# If hasFeedback is 0, it retrieves candidates without feedback.
def get_candidate_query(hasFeedback):
    """
    Returns a SQL query string to retrieve candidates based on the feedback status.
    Args:
        hasFeedback (int): 
            0 - retrieves candidates without feedback for the given position,
            1 - retrieves candidates with feedback for the given position,
            2 - retrieves all candidates regardless of feedback.

    Returns:
        str: SQL query string.
    """
    if hasFeedback == 0:
        return """
            SELECT 
                c.id, 
                c.proj_id, 
                c.candidate_info, 
                1 - (embedding <=> %s) AS cosine_similarity, 
                c.to_be_embebbed,
                COUNT(i.id) AS num_feedbacks
            FROM 
                candidates_smarthr c
            LEFT JOIN 
                interviews i ON i.candidate_id = c.id AND i.position_id = %s
            WHERE 
                c.is_deleted = false 
                AND c.is_active = true
            GROUP BY 
                c.id, c.proj_id, c.candidate_info, c.to_be_embebbed
            HAVING 
                COUNT(i.id) = 0
            ORDER BY 
                cosine_similarity DESC
            LIMIT %s
        """
    elif hasFeedback == 1:
        return """
            SELECT 
                c.id, 
                c.proj_id, 
                c.candidate_info, 
                1 - (embedding <=> %s) AS cosine_similarity, 
                c.to_be_embebbed,
                COUNT(i.id) AS num_feedbacks
            FROM 
                candidates_smarthr c
            LEFT JOIN 
                interviews i ON i.candidate_id = c.id
            WHERE 
                c.is_deleted = false 
                AND c.is_active = true  AND i.position_id = %s
            GROUP BY 
                c.id, c.proj_id, c.candidate_info, c.to_be_embebbed
            HAVING 
                COUNT(i.id) > 0
            ORDER BY 
                cosine_similarity DESC
            LIMIT %s
        """
    else:
        return """
            SELECT 
                id, 
                proj_id, 
                candidate_info, 
                1 - (embedding <=> %s) AS cosine_similarity, 
                to_be_embebbed
            FROM 
                candidates_smarthr 
            Where is_deleted = false and is_active = true
            ORDER BY cosine_similarity DESC
            LIMIT %s
        """


# This function processes candidates in batch mode.
# If batch_mode is True, it processes all candidates in a single prompt.
def _batch_mode_analysis(results, processed_position):
    try:
        # Step 1: Apply experience-based reranking (if enabled)
        reranking_config = MODELS_CONFIG.get('experience_reranking', {})
        if reranking_config.get('enabled', True):
            logger.info("Applying experience-based reranking to %d candidates", len(results))
            try:
                reranked_results = match_functions.rerank_candidates_by_experience(
                    results=results,
                    processed_position=processed_position,
                    experience_weight=reranking_config.get('experience_weight', 0.6),
                    similarity_weight=reranking_config.get('similarity_weight', 0.4),
                    models_order=reranking_config.get('models_order', MODELS_CONFIG['default_models_order']),
                    timeout_seconds=reranking_config.get('timeout_seconds', 300)
                )
                logger.info("Experience-based reranking completed successfully")
            except Exception as e:
                logger.warning("Experience-based reranking failed, using original order: %s", str(e))
                reranked_results = results if reranking_config.get('fallback_on_error', True) else results
        else:
            logger.info("Experience-based reranking is disabled, using original order")
            reranked_results = results

        candidates_result = []
        # Step 2: Process reranked candidates
        for idx, r in enumerate(reranked_results):

            candidate_id_db = r[0]
            proj_id_db = r[1]
            candidate_info_db = r[2]
            similarity = r[3] if len(r) > 3 else 0.0  # Default to 0.0 if not provided

            # Use skill-aware matching for custom analysis
            try:
                custom_analysis_summaries = match_functions.get_candidate_analysis_with_skill_priority(
                    candidate_text=r[4],
                    processed_position=processed_position
                )
            except Exception as e:
                # Fallback to old matching if skill-aware fails
                logger.warning(f"Skill-aware custom analysis failed, falling back to old matching: {e}")
                custom_analysis_summaries = match_functions.get_candidate_analysis_custom_prompt(
                    candidate_text=r[4],
                    processed_position=processed_position
                )

            # Append the results
            candidates_result.append({
                "id": candidate_id_db,
                "proj_id": proj_id_db,
                "candidate_info": candidate_info_db,
                "cosine_similarity": round(similarity * 100, 2) if similarity is not None else 0.0,  # Convert to percentage with two decimals
                "custom_analysis": custom_analysis_summaries
            })
        # Add batch summary to result
        final_match_response = {
            "matched_candidates": candidates_result,
            "processed_position": processed_position,
            "timestamp": datetime.now(),
        }
        logger.info("Returning %d candidates matched in batch mode", len(candidates_result))
        return final_match_response
    except Exception as e:
        logger.warning(f"Batch analysis failed, switching to parallel processing: {str(e)}")
        # batch_mode = False  # Switch to parallel processing


# This function processes candidates in parallel.
# It uses ThreadPoolExecutor to evaluate candidates against the position in parallel.
# It returns a structured response with matched candidates and their analyses.
def _process_candidates_parallel(results, processed_position):
    # Step 1: Apply experience-based reranking (if enabled)
    reranking_config = MODELS_CONFIG.get('experience_reranking', {})
    if reranking_config.get('enabled', True):
        logger.info("Applying experience-based reranking to %d candidates", len(results))
        try:
            reranked_results = match_functions.rerank_candidates_by_experience(
                results=results,
                processed_position=processed_position,
                experience_weight=reranking_config.get('experience_weight', 0.6),
                similarity_weight=reranking_config.get('similarity_weight', 0.4),
                models_order=reranking_config.get('models_order', MODELS_CONFIG['default_models_order']),
                timeout_seconds=reranking_config.get('timeout_seconds', 300)
            )
            logger.info("Experience-based reranking completed successfully")
        except Exception as e:
            logger.warning("Experience-based reranking failed, using original order: %s", str(e))
            reranked_results = results if reranking_config.get('fallback_on_error', True) else results
    else:
        logger.info("Experience-based reranking is disabled, using original order")
        reranked_results = results

    # Step 2: Process reranked candidates in parallel using ThreadPoolExecutor
    with log_time_block("Parallel Candidate Analysis"):
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(10, len(reranked_results))) as executor:
            # Create a partial function with the position already set
            evaluate_func = partial(process_candidate_parallel, processed_position=processed_position)
            # Map the function to all reranked results
            parallel_results = list(executor.map(evaluate_func, reranked_results))
            # Add results to candidates_result
            candidates_result = parallel_results
    final_match_response = {
        "matched_candidates": candidates_result,
        "processed_position": processed_position,
        "timestamp": datetime.now(),
    }
    logger.info("Returning %d candidates matched in parallel mode", len(candidates_result))
    return final_match_response


# Helper funtion to retrieve position embedding and text
# This function retrieves the position embedding and processed text from the database.
def _get_position_embedding_and_text(position_id):
    with get_cursor() as cur:
        # Retrieve position embedding & text
        logger.info("Retrieving position embedding and text for position_id=%s", position_id)
        cur.execute(
            "SELECT embedding, to_be_embebbed FROM positions_smarthr WHERE id=%s",
            (position_id,),
        )
        row = cur.fetchone()
        if not row:
            logger.warning("Position not found with id=%s", position_id)
            raise HTTPException(status_code=404, detail="Position not found")

        position_embedding = row[0]
        processed_position = row[1]
    return position_embedding, processed_position


# Helper function to search candidates for a position embedding
# This function searches for candidates based on the position embedding and hasFeedback status.
def _search_candidates_for_position_embedding(position_id, position_embedding, hasFeedback, limit):
    if hasFeedback not in [0, 1, 2]:
        logger.warning("Invalid hasFeedback value: %d", hasFeedback)
        raise HTTPException(status_code=400, detail="Invalid hasFeedback value")
    # Search for candidates
    query = get_candidate_query(hasFeedback)
    if hasFeedback in [0, 1]:
        # If hasFeedback is 0 or 1, we need to filter by position_id
        with get_cursor() as cur:
            cur.execute(query, (position_embedding, position_id, threshold_match))
            results = cur.fetchall()
    else:
        with get_cursor() as cur:
            cur.execute(query, (position_embedding, threshold_match))
            results = cur.fetchall()
    logger.info("Candidates found: %d", len(results))
    # take the limit elements
    return results[:limit]


# Helper function to retrieve candidate embedding and text
# This function retrieves the candidate embedding and processed text from the database.
def _get_candidate_embedding_and_text(candidate_id):
    with get_cursor() as cur:
        cur.execute(
            "SELECT embedding, to_be_embebbed FROM candidates_smarthr WHERE id=%s AND is_deleted = false and is_active = true",
            (candidate_id,),
        )
        row = cur.fetchone()
        if not row:
            logger.warning("Candidate not found with Id=%s", candidate_id)
            raise HTTPException(status_code=404, detail="Candidate not found")

        candidate_embedding = row[0]
        processed_candidate = row[1]
    return candidate_embedding, processed_candidate


# Helper function to search positions for a candidate embedding
# This function searches for positions based on the candidate embedding.
def _search_positions_for_candidate_embedding(candidate_embedding, limit):
    with get_cursor() as cur:
        cur.execute(
            """SELECT id, proj_id, position_info, 1 - (embedding <=> %s) AS cosine_similarity, to_be_embebbed
                FROM positions_smarthr
                WHERE position_info -> 'reasonStatus' IS NULL OR position_info -> 'reasonStatus' ->> 'reason' IS NULL OR TRIM(position_info -> 'reasonStatus' ->> 'reason') = ''
                ORDER BY cosine_similarity DESC
                LIMIT %s
            """,
            (candidate_embedding, limit),
        )
        results = cur.fetchall()
    logger.info("Positions found: %d", len(results))
    return results


# This endpoint matches a position with candidates or a candidate with positions.
@router.get("/match/custom")
def custom_match(
    position_id: str = Query(None, description="Position ID to match candidates against"), 
    candidates_id: List[str] = Query(..., description="List of candidate IDs"),
    user_id: str = Query(..., description="User ID")
):
    try:
        logger.info("Starting endpoint /match with position_id=%s candidates_id=%s", position_id, candidates_id)
        if not position_id:
            logger.warning("Position ID is required for matching candidates")
            raise HTTPException(status_code=400, detail="Position ID is required for matching candidates.")

        if not candidates_id or len(candidates_id) == 0:
            logger.warning("Candidates IDs are required for matching")
            raise HTTPException(status_code=400, detail="Candidates IDs are required for matching.")

        # Retrieve position embedding & text
        logger.info("Retrieved embedding for position Id=%s", position_id)
        position = get_position_by_id(position_id)
        if not position:
            logger.warning("Position not found with id=%s", position_id)
            raise HTTPException(status_code=404, detail=f"Position not found with the provided Id: {position_id}")

        return get_custom_match(position_id, candidates_id, user_id, user_id)
    except psycopg2.Error as db_error:
        logger.error("Database error: %s", db_error, exc_info=True)
        raise HTTPException(status_code=500, detail=f"Custom Match. execute_match. Database error occurred: {str(db_error)}")
    except HTTPException as http_exc:
        logger.error("Custom Match. HTTP Exception in endpoint /match/custom: %s", http_exc.detail, exc_info=True)
        raise http_exc
    except Exception as e:
        logger.error("Custom Match. Error occurred : %s", e, exc_info=True)
        raise HTTPException(status_code=500, detail="Custom Match. An error occurred while processing the match.")


# Return environment variables for debugging
@router.get("/env_variables")
def get_env_variables():
    import os
    env_vars = {
        "TENANT_ID": os.getenv("TENANT_ID"),
        "CLIENT_ID_SMART_HR": os.getenv("CLIENT_ID_SMART_HR"),
        "CLIENT_ID_POWER_APP": os.getenv("CLIENT_ID_POWER_APP"),
        "CLIENT_ID_FLOW_HR": os.getenv("CLIENT_ID_FLOW_HR"),
        "POSTGRES_USER": os.getenv("POSTGRES_USER"),
        "LUMUS_API_URL": os.getenv("LUMUS_API_URL"),
        "LUMUS_API_TIMEOUT": os.getenv("LUMUS_API_TIMEOUT"),
        "DEFAULT_MODELS_ORDER": os.getenv("DEFAULT_MODELS_ORDER"),
        "POSITION_MATCH_MODELS_ORDER": os.getenv("POSITION_MATCH_MODELS_ORDER"),
        "APPLICATIONINSIGHTS_CONNECTION_STRING": os.getenv("APPLICATIONINSIGHTS_CONNECTION_STRING"),
        "AZURE_OPENAI_ENDPOINT": os.getenv("AZURE_OPENAI_ENDPOINT"),
        "API_CALLBACK_URL": os.getenv("API_CALLBACK_URL"),
        "DEFAULT_INTERNAL_PROJECT_ID": os.getenv("DEFAULT_INTERNAL_PROJECT_ID"),
        "MAXIMUM_NUMBER_OF_MATCHES": os.getenv("MAXIMUM_NUMBER_OF_MATCHES"),
        # Add other relevant environment variables as needed
    }
    return env_vars


@router.get("/match/position")
def get_match_position(
    position_id: str = Query(..., description="Position ID to match candidates against"),
    limit: int = 5,
    user_id: str = Query(..., description="User ID")
):
    try:
        return get_matching_results_by_position(position_id, limit, user_id, user_id)
    except psycopg2.Error as e:
        logger.error(f"Database error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error occurred: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException occurred: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/match/candidate")
def get_match_candidate(
    candidate_id: str = Query(..., description="Candidate ID to match positions against"),
    limit: int = 5,
    user_id: str = Query(..., description="User ID")
):
    try:
        return get_matching_results_by_candidate(candidate_id, limit, user_id, user_id)
    except psycopg2.Error as e:
        logger.error(f"Database error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error occurred: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException occurred: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/match/all_positions")
def get_match_all_positions():
    try:
        positions = fetch_all_positions()
        for position in positions:
            get_matching_results_by_position(position.id, 1, 'system', 'system')
        return {"message": "Match generation started for all positions", "positions": positions}
    except psycopg2.Error as e:
        logger.error(f"Database error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error occurred: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException occurred: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/match/all_candidates")
def get_match_all_candidates():
    try:
        candidates = fetch_all_candidates()
        for candidate in candidates:
            get_matching_results_by_candidate(candidate.id, 1, 'system', 'system')
        return {"message": "Match generation started for all candidates", "candidates": candidates}
    except psycopg2.Error as e:
        logger.error(f"Database error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error occurred: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException occurred: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
