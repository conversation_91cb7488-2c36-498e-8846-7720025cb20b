# Standard library imports
import logging
from typing import List, Dict, Any

# Third-party imports
from fastapi import APIRouter, Body, HTTPException, Path, Query
import psycopg2

# Internal imports
from controllers.interview_controller import (
    generate_and_persist_qa,
    run_and_persist_interview,
    create_interviews_for_position,
    fetch_all_interviews_by_position_id,
    update_interview_tec,
    update_interview_hr,
    fetch_interview_by_position_id_candidate_id,
    fetch_questions_by_position_id,
    delete_interview,
    fetch_interviews_by_candidate_id,
    fetch_interview_by_interview_id,
    update_question_regeneration_status,
    # 4-Agent System Functions
    four_agent_evaluate_interview,
    evaluate_interview_with_four_agents
)
from models.interview import InterviewCreate, ProcessType, Interview, InterviewTec, InterviewHr, FourAgentEvaluationRequest, FourAgentEvaluationResult
from models.models import SingleQuestions

# Telemetry Section
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
router = APIRouter()


# Generate and persist interview questions for a given position.
@router.post("/{position_id}/questions", response_model=Dict[str, Any])
def generate_questions(
    position_id: str = Path(..., description="UUID of the position"),
    n_questions: int = Query(10, description="Number of questions to generate", ge=1, le=20),
    include: str = Query("", description="Topics to include in the questions, separated by commas"),
    current_user: str = Query(..., description="Current user")
):
    """
    Generate and persist interview questions for a given position.

    Parameters
    ----------
    position_id : str
        UUID of the position for which questions are generated.
    n_questions : int
        Number of questions to generate (default is 10, min 1, max 20).
    include : str
        Comma-separated topics to include in the questions.
    current_user : str
        Identifier for the current user.

    Returns
    -------
    Dict[str, Any]
        The generated questions and related data.

    Raises
    ------
    HTTPException
        If question generation fails or a database error occurs.
    """
    try:
        logger.info(
            f"Generating {n_questions} questions for position_id: {position_id} with topics: '{include}'"
        )
        questions_data = generate_and_persist_qa(
            position_id=position_id,
            n_questions=n_questions,
            include=include,
            current_user=current_user
        )
        if not questions_data:
            raise HTTPException(status_code=404, detail="Failed to generate questions")
        return questions_data.model_dump()
    except psycopg2.Error as db_error:
        logger.error(f"Database error during question generation: {db_error}")
        raise HTTPException(status_code=500, detail=f"Database error: {db_error}")
    except HTTPException as http_exc:
        logger.error(f"QA generation failed: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"QA generation failed: {exc}")
        raise HTTPException(status_code=500, detail=f"QA generation failed: {str(exc)}")


# Extract and persist answers from an interview transcript.
@router.post("/{interview_id}/answers")
def extract_answers(
    interview_id: str = Path(..., description="UUID of the interview"),
    process_type: ProcessType = ProcessType.EXTRACT,
):
    """
    Extract and persist answers from an interview transcript.

    Parameters
    ----------
    interview_id : str
        UUID of the interview to process.
    process_type : ProcessType
        Type of processing to perform (default is EXTRACT).

    Returns
    -------
    dict
        Extracted answers and related data.

    Raises
    ------
    HTTPException
        If extraction fails or a database error occurs.
    """
    try:
        logger.info(f"Extracting answers for interview_id: {interview_id} with process_type: {process_type}")
        extraction_result = run_and_persist_interview(interview_id, process_type)
        return extraction_result.model_dump()
    except psycopg2.Error as db_error:
        logger.error(f"Database error during answer extraction: {db_error}")
        raise HTTPException(status_code=500, detail=f"Database error: {db_error}")
    except HTTPException as http_exc:
        logger.error(f"Answer extraction failed: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Answer extraction failed: {exc}")
        raise HTTPException(status_code=500, detail=f"Answer extraction failed: {str(exc)}")


# Re-evaluate an interview by its ID.
@router.post("/{interview_id}/evaluate", response_model=Interview)
def evaluate_interview(interview_id: str = Path(..., description="UUID of the interview")):
    """
    Re-evaluate an interview by its ID using the enhanced 4-agent system.

    This endpoint now uses the new 4-agent evaluation system that properly handles
    invalid responses like "mmmmmm" and provides more accurate scoring. Falls back
    to the legacy system if the 4-agent system fails.

    Parameters
    ----------
    interview_id : str
        UUID of the interview to be re-evaluated.

    Returns
    -------
    Interview
        The updated interview object after re-evaluation.

    Raises
    ------
    HTTPException
        If the interview is not found or a database error occurs.
    """
    try:
        logger.info(f"Re-evaluating interview with interview_id: {interview_id}")
        logger.info(f"MAIN ENDPOINT: Using enhanced 4-agent evaluation system")

        # Use the new 4-agent evaluation system (with fallback to old system)
        evaluation_result = evaluate_interview_with_four_agents(interview_id)

        if not evaluation_result:
            raise HTTPException(status_code=404, detail="Interview not found (enhanced evaluation)")

        # Log the evaluation result type and details
        if hasattr(evaluation_result, 'overall_seniority'):
            logger.info(f"MAIN ENDPOINT: 4-agent evaluation completed - {evaluation_result.overall_seniority} ({evaluation_result.percentage_of_correct_answers}%)")
        else:
            logger.info(f"MAIN ENDPOINT: Legacy evaluation completed")

        # Fetch and return the complete Interview object with all required fields
        interview = fetch_interview_by_interview_id(interview_id)

        if not interview:
            raise HTTPException(status_code=404, detail="Interview not found after evaluation")

        return interview
    except psycopg2.Error as db_error:
        logger.error(f"Database error during interview evaluation: {db_error}")
        raise HTTPException(status_code=500, detail=f"Database error: {db_error}")
    except HTTPException as http_exc:
        logger.error(f"Interview evaluation failed: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Interview evaluation failed: {exc}")
        raise HTTPException(status_code=500, detail=f"Evaluation failed: {str(exc)}")


# Create interviews for a given position and a list of candidates.
@router.post("/{position_id}/create", response_model=List[Interview])
def create_interviews_for_candidates(
    position_id: str = Path(..., description="UUID of the position"),
    interview_data_list: list[InterviewCreate] = Body(..., description="List of interview data for candidates")
):
    """
    Create interviews for a given position and a list of candidates.

    Parameters
    ----------
    position_id : str
        UUID of the position for which interviews are being created.
    interview_data_list : list[InterviewCreate]
        List of interview data objects, each containing candidate information.

    Returns
    -------
    List[Interview]
        List of created interview objects.

    Raises
    ------
    HTTPException
        If no candidate IDs are provided, position ID is missing, or interview creation fails.
    """
    try:
        candidate_ids = [interview_data.candidate_id for interview_data in interview_data_list]
        logger.info(f"Creating interviews for position_id: {position_id} and candidate_ids: {candidate_ids}")

        if not candidate_ids:
            raise HTTPException(status_code=400, detail="At least one candidate ID is required")
        if not position_id:
            raise HTTPException(status_code=400, detail="Position ID is required")

        created_interviews = create_interviews_for_position(position_id, interview_data_list)
        if not created_interviews:
            raise HTTPException(status_code=404, detail="Failed to create interviews")
        return created_interviews

    except psycopg2.Error as db_error:
        logger.error(f"Database error during interview creation: {db_error}")
        raise HTTPException(status_code=500, detail=f"Database error: {db_error}")
    except HTTPException as http_exc:
        logger.error(f"Interview creation failed: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Interview creation failed: {exc}")
        raise HTTPException(status_code=500, detail=f"Interview creation failed: {str(exc)}")


# Retrieve all interviews associated with a specific position ID.
@router.get("/{position_id}", response_model=List[Interview])
def get_interviews_by_position_id(
    position_id: str = Path(..., description="UUID of the position")
):
    """
    Retrieve all interviews associated with a specific position ID.

    Parameters
    ----------
    position_id : str
        UUID of the position for which to retrieve interviews.

    Returns
    -------
    List[Interview]
        List of interview objects associated with the position.

    Raises
    ------
    HTTPException
        If a database error occurs or fetching interviews fails.
    """
    try:
        logger.info(f"Fetching interviews for position_id: {position_id}")
        interviews = fetch_all_interviews_by_position_id(position_id)
        return interviews or []
    except psycopg2.Error as db_error:
        logger.error(f"Database error during interview list fetch: {db_error}")
        raise HTTPException(status_code=500, detail=f"Database error: {db_error}")
    except HTTPException as http_exc:
        logger.error(f"Interview list fetch failed: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Interview list fetch failed: {exc}")
        raise HTTPException(status_code=500, detail=f"Fetching interview list failed: {str(exc)}")


# Update the details of a technical interview.
@router.put("/tec", response_model=Interview)
def update_technical_interview(interview_tec_data: InterviewTec):
    """
    Update the details of a technical interview.

    Parameters
    ----------
    interview_tec_data : InterviewTec
        The technical interview data to update.

    Returns
    -------
    Interview
        The updated interview object.

    Raises
    ------
    HTTPException
        If the interview is not found or a database error occurs.
    """
    try:
        logger.info(f"Updating technical interview with data: {interview_tec_data}")
        updated_interview = update_interview_tec(interview_tec_data)
        if not updated_interview:
            raise HTTPException(status_code=404, detail="Interview not found (technical update)")
        return updated_interview
    except psycopg2.Error as db_error:
        logger.error(f"Database error during technical interview update: {db_error}")
        raise HTTPException(status_code=500, detail=f"Database error: {db_error}")
    except HTTPException as http_exc:
        logger.error(f"Technical interview update failed: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Technical interview update failed: {exc}")
        raise HTTPException(status_code=500, detail=f"Technical interview update failed: {str(exc)}")


# Update the details of an HR interview.
@router.put("/hr", response_model=Interview)
def update_hr_interview(interview_hr_data: InterviewHr):
    """
    Update the details of an HR interview.

    Parameters
    ----------
    interview_hr_data : InterviewHr
        The HR interview data to update.

    Returns
    -------
    Interview
        The updated interview object.

    Raises
    ------
    HTTPException
        If the interview is not found or a database error occurs.
    """
    try:
        logger.info(f"Updating HR interview with data: {interview_hr_data}")
        updated_interview = update_interview_hr(interview_hr_data)
        if not updated_interview:
            raise HTTPException(status_code=404, detail="Interview not found (hr update)")
        return updated_interview
    except psycopg2.Error as db_error:
        logger.error(f"Database error during HR interview update: {db_error}")
        raise HTTPException(status_code=500, detail=f"Database error: {db_error}")
    except HTTPException as http_exc:
        logger.error(f"HR interview update failed: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"HR interview update failed: {exc}")
        raise HTTPException(status_code=500, detail=f"HR interview update failed: {str(exc)}")


# Retrieve a single interview for a specific position and candidate.
@router.get("/{position_id}/{candidate_id}", response_model=Interview)
def get_single_interview_by_position_and_candidate(
    position_id: str = Path(..., description="UUID of the position"),
    candidate_id: str = Path(..., description="UUID of the candidate")
):
    """
    Retrieve a single interview for a specific position and candidate.

    Parameters
    ----------
    position_id : str
        UUID of the position.
    candidate_id : str
        UUID of the candidate.

    Returns
    -------
    Interview
        The interview object for the specified position and candidate.

    Raises
    ------
    HTTPException
        If the interview is not found or a database error occurs.
    """
    try:
        logger.info(
            f"Fetching interview for position_id: {position_id} and candidate_id: {candidate_id}"
        )
        interview = fetch_interview_by_position_id_candidate_id(position_id, candidate_id)
        if not interview:
            raise HTTPException(status_code=404, detail="Interview not found")
        return interview
    except psycopg2.Error as db_error:
        logger.error(f"Database error during interview fetch: {db_error}")
        raise HTTPException(status_code=500, detail=f"Database error: {db_error}")
    except HTTPException as http_exc:
        logger.error(f"Interview fetch failed: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Interview fetch failed: {exc}")
        raise HTTPException(status_code=500, detail=f"Fetching interview failed: {str(exc)}")


# Retrieve interview questions for a specific position.
@router.get("/questions/", response_model=SingleQuestions)
def get_interview_questions_by_position(
    position_id: str = Query(..., description="UUID of the position")
):
    """
    Retrieve interview questions for a specific position.

    Parameters
    ----------
    position_id : str
        UUID of the position for which to retrieve interview questions.

    Returns
    -------
    SingleQuestions
        The questions associated with the specified position.

    Raises
    ------
    HTTPException
        If the position ID is missing, no questions are found, or a database error occurs.
    """
    try:
        logger.info(f"Fetching questions for position_id: {position_id}")
        if not position_id:
            raise HTTPException(status_code=400, detail="Position ID is required")

        questions = fetch_questions_by_position_id(position_id)
        if not questions:
            raise HTTPException(status_code=404, detail="Questions not found")
        return questions
    except psycopg2.Error as db_error:
        logger.error(f"Database error during interview questions fetch: {db_error}")
        raise HTTPException(status_code=500, detail=f"Database error: {db_error}")
    except HTTPException as http_exc:
        logger.error(f"Interview questions fetch failed: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Interview questions fetch failed: {exc}")
        raise HTTPException(status_code=500, detail=f"Fetching interview questions failed: {str(exc)}")


# Delete an interview for a specific position and candidate.
@router.delete("/{position_id}/{candidate_id}")
def delete_interview_by_position_and_candidate(
    position_id: str = Path(..., description="UUID of the position"),
    candidate_id: str = Path(..., description="UUID of the candidate")
):
    """
    Delete an interview for a specific position and candidate.

    Parameters
    ----------
    position_id : str
        UUID of the position.
    candidate_id : str
        UUID of the candidate.

    Returns
    -------
    Any
        The result of the delete operation.

    Raises
    ------
    HTTPException
        If the interview is not found or a database error occurs.
    """
    try:
        logger.info(
            f"Deleting interview for position_id: {position_id} and candidate_id: {candidate_id}"
        )
        result = delete_interview(position_id, candidate_id)
        if not result:
            raise HTTPException(status_code=404, detail="Interview not found")
        return result
    except psycopg2.Error as db_error:
        logger.error(f"Database error during interview deletion: {db_error}")
        raise HTTPException(status_code=500, detail=f"Database error: {db_error}")
    except HTTPException as http_exc:
        logger.error(f"Interview deletion failed: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Interview deletion failed: {exc}")
        raise HTTPException(status_code=500, detail=f"Interview deletion failed: {str(exc)}")


# Retrieve all interviews associated with a specific candidate ID.
@router.get("/", response_model=List[Interview])
def get_interviews_by_candidate(candidate_id: str = Query(..., description="UUID of the candidate")):
    """
    Retrieve all interviews associated with a specific candidate ID.

    Parameters
    ----------
    candidate_id : str
        UUID of the candidate for whom to retrieve interviews.

    Returns
    -------
    List[Interview]
        List of interview objects associated with the candidate.

    Raises
    ------
    HTTPException
        If a database error occurs or fetching interviews fails.
    """
    try:
        logger.info(f"Fetching interviews for candidate_id: {candidate_id}")
        interviews = fetch_interviews_by_candidate_id(candidate_id)
        return interviews or []
    except psycopg2.Error as db_error:
        logger.error(f"Database error during interviews fetch by candidate: {db_error}")
        raise HTTPException(status_code=500, detail=f"Database error: {db_error}")
    except HTTPException as http_exc:
        logger.error(f"Interviews fetch by candidate failed: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Interviews fetch by candidate failed: {exc}")
        raise HTTPException(status_code=500, detail=f"Fetching interviews by candidate failed: {str(exc)}")


# Retrieve a single interview by its interview ID.
@router.get("", response_model=Interview)
def get_interview_by_id(
    interview_id: str = Query(..., description="UUID of the interview")
):
    """
    Retrieve a single interview by its interview ID.

    Parameters
    ----------
    interview_id : str
        UUID of the interview to retrieve.

    Returns
    -------
    Interview
        The interview object for the specified interview ID.

    Raises
    ------
    HTTPException
        If the interview is not found or a database error occurs.
    """
    try:
        logger.info(f"Fetching interview for interview_id: {interview_id}")
        interview = fetch_interview_by_interview_id(interview_id)
        if not interview:
            raise HTTPException(status_code=404, detail="Interview not found (get_interview_by_id)")
        return interview
    except psycopg2.Error as db_error:
        logger.error(f"Database error during interview fetch: {db_error}")
        raise HTTPException(status_code=500, detail=f"Database error: {db_error}")
    except HTTPException as http_exc:
        logger.error(f"Interview fetch failed: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Interview fetch failed: {exc}")
        raise HTTPException(status_code=500, detail=f"Fetching interview failed: {str(exc)}")


# Update the regeneration status of a specific interview question.
@router.put("/questions/status", response_model=bool)
def update_question_regeneration_status_endpoint(
    position_id: str = Query(..., description="UUID of the position"),
    question_id: str = Query(..., description="UUID of the question"),
    allow_regeneration: bool = Query(True, description="Flag to allow regeneration of the question")
):
    """
    Update the regeneration status of a specific interview question.

    Parameters
    ----------
    position_id : str
        UUID of the position containing the question.
    question_id : str
        UUID of the question to update.
    allow_regeneration : bool
        Flag indicating whether regeneration is allowed for the question.

    Returns
    -------
    bool
        True if the status was updated successfully, otherwise raises an HTTPException.

    Raises
    ------
    HTTPException
        If the question is not found or a database error occurs.
    """
    try:
        logger.info(
            f"Updating regeneration status for question_id: {question_id} in position_id: {position_id} "
            f"to allow_regeneration={allow_regeneration}"
        )
        update_result = update_question_regeneration_status(position_id, question_id, allow_regeneration)
        if not update_result:
            raise HTTPException(status_code=404, detail="Question not found")
        return update_result
    except psycopg2.Error as db_error:
        logger.error(f"Database error during question regeneration status update: {db_error}")
        raise HTTPException(status_code=500, detail=f"Database error: {db_error}")
    except HTTPException as http_exc:
        logger.error(f"Question regeneration status update failed: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Question regeneration status update failed: {exc}")
        raise HTTPException(status_code=500, detail=f"Changing question regeneration status failed: {str(exc)}")


# Enhanced evaluation endpoint using 4-agent system
@router.post("/{interview_id}/evaluate-enhanced", response_model=Dict[str, Any])
def evaluate_interview_enhanced(
    interview_id: str = Path(..., description="UUID of the interview to evaluate")
):
    """
    Evaluate interview using the enhanced 4-agent system that properly handles invalid responses.

    This endpoint uses the new 4-agent evaluation architecture that:
    1. Parses transcript into structured Q&A pairs
    2. Extracts expected responses by seniority level
    3. Evaluates each question individually with proper invalid response handling
    4. Creates comprehensive overall evaluation with accurate percentage calculation

    The system properly handles improper answers like "mmmmmm" by excluding them from scoring.

    Parameters
    ----------
    interview_id : str
        UUID of the interview to evaluate.

    Returns
    -------
    Dict[str, Any]
        Enhanced evaluation result with detailed question-by-question analysis.

    Raises
    ------
    HTTPException
        If interview not found or evaluation fails.
    """
    try:
        logger.info(f"Starting enhanced 4-agent evaluation for interview {interview_id}")

        # Use the enhanced evaluation function
        result = evaluate_interview_with_four_agents(interview_id)

        logger.info(f"Enhanced evaluation completed for interview {interview_id}")
        logger.info(f"Result: {result.overall_seniority} seniority, {result.percentage_of_correct_answers}% score")

        return {
            "interview_id": interview_id,
            "evaluation_type": "4-agent-enhanced",
            "result": result.model_dump(),
            "message": "Enhanced evaluation completed successfully"
        }

    except HTTPException as http_exc:
        logger.error(f"Enhanced evaluation failed for interview {interview_id}: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Enhanced evaluation failed for interview {interview_id}: {str(exc)}")
        raise HTTPException(status_code=500, detail=f"Enhanced evaluation failed: {str(exc)}")


# Direct 4-agent evaluation endpoint
@router.post("/{interview_id}/evaluate-four-agents", response_model=FourAgentEvaluationResult)
def evaluate_interview_four_agents_direct(
    interview_id: str = Path(..., description="UUID of the interview to evaluate"),
    force_reevaluation: bool = Query(False, description="Force re-evaluation even if already evaluated")
):
    """
    Direct access to the 4-agent evaluation system.

    This endpoint provides direct access to the new 4-agent evaluation system
    without fallback to legacy evaluation methods.

    Parameters
    ----------
    interview_id : str
        UUID of the interview to evaluate.
    force_reevaluation : bool
        Whether to force re-evaluation even if already evaluated.

    Returns
    -------
    FourAgentEvaluationResult
        Complete 4-agent evaluation result.

    Raises
    ------
    HTTPException
        If interview not found or 4-agent evaluation fails.
    """
    try:
        logger.info(f"Starting direct 4-agent evaluation for interview {interview_id}")

        request = FourAgentEvaluationRequest(
            interview_id=interview_id,
            force_reevaluation=force_reevaluation
        )

        result = four_agent_evaluate_interview(request)

        logger.info(f"Direct 4-agent evaluation completed for interview {interview_id}")
        logger.info(f"Result: {result.overall_seniority} seniority, {result.percentage_of_correct_answers}% score")

        return result

    except HTTPException as http_exc:
        logger.error(f"Direct 4-agent evaluation failed for interview {interview_id}: {http_exc.detail}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Direct 4-agent evaluation failed for interview {interview_id}: {str(exc)}")
        raise HTTPException(status_code=500, detail=f"Direct 4-agent evaluation failed: {str(exc)}")
