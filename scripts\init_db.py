import os
import psycopg2
from dotenv import load_dotenv
load_dotenv()

user = os.getenv("POSTGRES_USER", "postgres")
password = os.getenv("POSTGRES_PASSWORD", "")
host = os.getenv("POSTGRES_HOST", "localhost")
port = os.getenv("POSTGRES_PORT", "5432")
database = os.getenv("POSTGRES_DB", "postgres")

connection = psycopg2.connect(
    user=user, password=password, host=host, port=port, database=database
)

cursor = connection.cursor()

# Create pgvector extension if not exists
cursor.execute("CREATE EXTENSION IF NOT EXISTS vector;")

# Create fuzzymatch extension if not exists
cursor.execute("CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;")

# Projects table with UUID primary key
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_name TEXT,
    name TEXT,
    description TEXT,
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
"""
)

# Candidates table with UUID primary key
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS candidates_smarthr (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    proj_id UUID REFERENCES projects(id),
    candidate_info JSONB,
    suggested_positions JSONB,
    analysis_status TEXT,
    to_be_embebbed TEXT,
    embedding VECTOR(1536),
    sparse_embedding JSONB,
    Reason_Info JSONB,
    is_active BOOLEAN DEFAULT true,
    is_deleted BOOLEAN DEFAULT false,
    last_matching TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT NOW(),
    updated_by TEXT
);
"""
)

# Positions table with UUID primary key
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS positions_smarthr (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    proj_id UUID REFERENCES projects(id),
    position_info JSONB,
    top_candidates JSONB,
    to_be_embebbed TEXT,
    embedding VECTOR(1536),
    sparse_embedding JSONB,
    last_matching TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    external_id TEXT
);
"""
)

# Create indexes
cursor.execute(
    """
CREATE INDEX IF NOT EXISTS candidates_embedding_idx 
ON candidates_smarthr USING hnsw (embedding vector_cosine_ops);
"""
)

# -------------------- ENUM interview_status ------------------------
cursor.execute(
    """
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'interview_status') THEN
        CREATE TYPE interview_status AS ENUM
            ('scheduled', 'in_progress', 'completed', 'cancelled', 'not_scheduled');
    END IF;
END $$;
"""
)

# ------------------ TABLE interview_questions ----------------------
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS interview_questions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    position_id UUID NOT NULL
               REFERENCES positions_smarthr(id) ON DELETE CASCADE,
    data JSONB,
    allow_regeneration BOOLEAN DEFAULT TRUE, -- new column to saved question flag and do not allow regeneration
    created_at TIMESTAMP DEFAULT NOW(),
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT NOW(),
    updated_by TEXT,
    UNIQUE (position_id)
);
"""
)

# ----------------------- TABLE interviews --------------------------
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS interviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    position_id  UUID NOT NULL
                 REFERENCES positions_smarthr(id) ON DELETE CASCADE,
    candidate_id UUID NOT NULL
                 REFERENCES candidates_smarthr(id) ON DELETE CASCADE,
    analysis_data JSONB,
    feedback_hr        JSONB,
    interview_date_hr  TIMESTAMP,
    feedback_date_hr   TIMESTAMP,
    recruiter_hr_id    TEXT,
    scheduled_hr_id    TEXT,
    status_hr          interview_status,
    recommendation_hr  BOOLEAN,
    transcript_hr      TEXT,

    feedback_tec       JSONB,
    recruiter_tec_id   TEXT,
    scheduled_tec_id   TEXT,
    interview_date_tec TIMESTAMP,
    feedback_date_tec  TIMESTAMP,
    status_tec         interview_status,
    recommendation_tec BOOLEAN,
    transcript_tec     TEXT,

    anwers_data   JSONB,
    interview_data JSONB,

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
"""
)


# ----------------------- TABLE candidate_notes ------------------------
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS candidate_notes (
    id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    candidate_id    UUID NOT NULL REFERENCES candidates_smarthr(id) ON DELETE CASCADE,
    notes           JSONB,
    created_by      TEXT,
    created_at      TIMESTAMP DEFAULT NOW(),
    updated_by      TEXT,
    updated_at      TIMESTAMP DEFAULT NOW()
);
"""
)

# ----------------------- TABLE professionals ------------------------
cursor.execute(
    """
    CREATE TABLE IF NOT EXISTS professionals (
        id                  UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        professional_info   JSONB,
        source          TEXT,
        reason          TEXT,
        to_be_embebbed  TEXT,
        embedding       VECTOR(1536),
        is_active       BOOLEAN DEFAULT true,
        is_deleted      BOOLEAN DEFAULT false,
        created_at      TIMESTAMP DEFAULT NOW(),
        created_by      TEXT,
        updated_at      TIMESTAMP DEFAULT NOW(),
        updated_by      TEXT
    );
    """
)


# ----------------------- TABLE smart_matching_results ------------------------
cursor.execute(
    """
    CREATE TABLE IF NOT EXISTS smart_matching_results (
        id                  UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        position_id         UUID REFERENCES positions_smarthr(id) ON DELETE CASCADE,
        candidate_id        UUID REFERENCES candidates_smarthr(id) ON DELETE CASCADE,
        cosine_similarity   FLOAT,
        analysis_data       JSONB,
        last_position_update TIMESTAMP,
        last_candidate_update TIMESTAMP,
        created_at          TIMESTAMP DEFAULT NOW(),
        created_by          TEXT,
        updated_at          TIMESTAMP DEFAULT NOW(),
        updated_by          TEXT
    );
    """
)

# add index to smart_matching_results
cursor.execute(
    """
    CREATE INDEX IF NOT EXISTS idx_smart_matching_results_position
    ON smart_matching_results(position_id);
    """
)
# add constraint to smart_matching_results
cursor.execute(
    """
    if not exists (select 1 from pg_constraint where conname = 'unique_position_candidate') then
        ALTER TABLE smart_matching_results
        ADD CONSTRAINT unique_position_candidate
        UNIQUE (position_id, candidate_id);
    end if;    
    """
)


# ----------------------- TRIGGER updated_at ------------------------
cursor.execute(
    """
CREATE OR REPLACE FUNCTION trg_set_updated_at()
RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;
"""
)

cursor.execute("DROP TRIGGER IF EXISTS set_updated_at_interviews ON interviews;")
cursor.execute(
    """
CREATE TRIGGER set_updated_at_interviews
BEFORE UPDATE ON interviews
FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();
"""
)

# --------------------------- INDEXES -------------------------------
cursor.execute(
    "CREATE INDEX IF NOT EXISTS idx_interviews_position   ON interviews(position_id);"
)
cursor.execute(
    "CREATE INDEX IF NOT EXISTS idx_interviews_candidate  ON interviews(candidate_id);"
)
cursor.execute(
    "CREATE INDEX IF NOT EXISTS idx_interviews_status_hr  ON interviews(status_hr);"
)
cursor.execute(
    "CREATE INDEX IF NOT EXISTS idx_interviews_status_tec ON interviews(status_tec);"
)
connection.commit()
cursor.close()
connection.close()
