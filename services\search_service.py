"""
Search service implementing the Adapter Pattern for professional data search.
Coordinates searches across multiple providers and standardizes responses.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import HTTPException

from providers.base_provider import BaseProvider, SearchRequest, ProviderResponse
from providers.linkedin_provider import LinkedInProvider
from models.professionals import Professional, ProfessionalCreate

# Setup logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class SearchService:
    """
    Main adapter service that coordinates searches across multiple providers.
    Implements the Adapter Pattern to standardize all provider responses.
    """
    
    def __init__(self):
        self.providers: Dict[str, BaseProvider] = {}
        self._initialize_providers()
        logger.info("Search service initialized with providers: %s", list(self.providers.keys()))
    
    def _initialize_providers(self):
        """Initialize all available providers"""
        try:
            # Initialize LinkedIn provider
            linkedin_provider = LinkedInProvider()
            self.providers["linkedin"] = linkedin_provider
            
            # Future providers can be added here:
            # self.providers["indeed"] = IndeedProvider()
            # self.providers["glassdoor"] = GlassdoorProvider()
            
        except Exception as e:
            logger.error(f"Error initializing providers: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to initialize search providers: {str(e)}")
    
    async def search_professionals(
        self,
        query: str,
        providers: Optional[List[str]] = None,
        location: Optional[str] = None,
        skills: Optional[List[str]] = None,
        experience_level: Optional[str] = None,
        limit: Optional[int] = 10,
        created_by: str = "search_service"
    ) -> Dict[str, Any]:
        """
        Search for professionals across multiple providers.
        
        Args:
            query: Search query string
            providers: List of provider names to search (default: all available)
            location: Location filter
            skills: Skills filter
            experience_level: Experience level filter
            limit: Maximum results per provider
            created_by: User who initiated the search
            
        Returns:
            Dict containing unified search results from all providers
        """
        try:
            logger.info(f"Starting professional search for query: {query}")
            
            # Use all providers if none specified
            if not providers:
                providers = list(self.providers.keys())
            
            # Validate requested providers
            invalid_providers = [p for p in providers if p not in self.providers]
            if invalid_providers:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Invalid providers: {invalid_providers}. Available: {list(self.providers.keys())}"
                )
            
            # Create search request
            search_request = SearchRequest(
                query=query,
                location=location,
                skills=skills,
                experience_level=experience_level,
                limit=limit
            )
            
            # Search across all requested providers
            all_results = []
            provider_metadata = {}
            
            for provider_name in providers:
                try:
                    logger.info(f"Searching provider: {provider_name}")
                    provider = self.providers[provider_name]
                    
                    # Get results from provider
                    provider_response = await provider.search_professionals(search_request)
                    
                    # Convert to Professional models and add metadata
                    for professional_data in provider_response.professionals:
                        professional_model = self._create_professional_model(
                            professional_data, 
                            provider_name, 
                            created_by
                        )
                        all_results.append(professional_model)
                    
                    # Store provider metadata
                    provider_metadata[provider_name] = {
                        "total_found": provider_response.total_found,
                        "search_metadata": provider_response.search_metadata
                    }
                    
                    logger.info(f"Provider {provider_name} returned {len(provider_response.professionals)} results")
                    
                except Exception as e:
                    logger.error(f"Error searching provider {provider_name}: {str(e)}")
                    # Continue with other providers even if one fails
                    provider_metadata[provider_name] = {
                        "error": str(e),
                        "total_found": 0
                    }
            
            # Prepare unified response
            response = {
                "professionals": all_results,
                "total_results": len(all_results),
                "providers_searched": providers,
                "provider_metadata": provider_metadata,
                "search_parameters": {
                    "query": query,
                    "location": location,
                    "skills": skills,
                    "experience_level": experience_level,
                    "limit_per_provider": limit
                },
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Search completed. Total results: {len(all_results)}")
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error in search_professionals: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")
    
    def _create_professional_model(
        self, 
        professional_data: Dict[str, Any], 
        provider_name: str, 
        created_by: str
    ) -> Dict[str, Any]:
        """
        Create a Professional model from provider data.
        
        Args:
            professional_data: Standardized professional data from provider
            provider_name: Name of the source provider
            created_by: User who initiated the search
            
        Returns:
            Dict representing a Professional model
        """
        try:
            # Ensure source is set
            professional_data["source"] = provider_name
            
            # Generate text for embedding
            to_be_embedded = self._generate_embedding_text(professional_data)
            
            # Create Professional model data
            professional_model = {
                "professional_info": professional_data,
                "source": provider_name,
                "to_be_embebbed": to_be_embedded,
                "is_active": True,
                "is_deleted": False,
                "created_by": created_by,
                "reason": f"Imported from {provider_name} search"
            }
            
            return professional_model
            
        except Exception as e:
            logger.error(f"Error creating professional model: {str(e)}")
            raise
    
    def _generate_embedding_text(self, professional_data: Dict[str, Any]) -> str:
        """
        Generate text for embedding from professional data.
        
        Args:
            professional_data: Professional data dictionary
            
        Returns:
            str: Text suitable for embedding generation
        """
        text_parts = []
        
        # Add summary
        if professional_data.get("summary"):
            text_parts.append(professional_data["summary"])
        
        # Add roles
        if professional_data.get("roles"):
            text_parts.append("Roles: " + ", ".join(professional_data["roles"]))
        
        # Add skills
        if professional_data.get("skills"):
            skill_names = [skill.get("name", "") for skill in professional_data["skills"] if isinstance(skill, dict)]
            if skill_names:
                text_parts.append("Skills: " + ", ".join(skill_names))
        
        # Add work experience
        if professional_data.get("work_experience"):
            for exp in professional_data["work_experience"]:
                if isinstance(exp, dict):
                    exp_text = f"{exp.get('job_title', '')} at {exp.get('company_name', '')}"
                    if exp.get("responsibilities"):
                        exp_text += ". " + " ".join(exp["responsibilities"][:2])  # First 2 responsibilities
                    text_parts.append(exp_text)
        
        # Add education
        if professional_data.get("education"):
            for edu in professional_data["education"]:
                if isinstance(edu, dict):
                    edu_text = f"{edu.get('degree', '')} in {edu.get('field_of_study', '')} from {edu.get('institution_name', '')}"
                    text_parts.append(edu_text)
        
        return " | ".join(text_parts)
    
    def get_available_providers(self) -> List[str]:
        """Get list of available provider names"""
        return list(self.providers.keys())
    
    def get_provider_info(self, provider_name: str) -> Dict[str, Any]:
        """
        Get information about a specific provider.
        
        Args:
            provider_name: Name of the provider
            
        Returns:
            Dict with provider information
        """
        if provider_name not in self.providers:
            raise HTTPException(status_code=404, detail=f"Provider {provider_name} not found")
        
        provider = self.providers[provider_name]
        return {
            "name": provider.get_provider_name(),
            "type": type(provider).__name__,
            "available": True
        }


# Global search service instance
search_service = SearchService()
