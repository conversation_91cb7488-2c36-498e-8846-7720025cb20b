#!/usr/bin/env python3
"""
Test script to analyze the interview evaluation system with the provided transcript data.
This script simulates the 4-agent evaluation process to understand the scoring behavior.
"""

import json
from typing import List, Dict, Any
from enum import Enum

class Seniority(str, Enum):
    SENIOR = "senior"
    MID = "mid"
    JUNIOR = "junior"
    NA = "n/a"

class MockIndividualEvaluation:
    """Mock class to simulate IndividualQuestionEvaluation"""
    def __init__(self, question_number: int, is_valid_response: bool, 
                 is_dont_know_response: bool, detected_seniority: Seniority):
        self.question_number = question_number
        self.is_valid_response = is_valid_response
        self.is_dont_know_response = is_dont_know_response
        self.detected_seniority = detected_seniority

def simulate_agent4_scoring(individual_evaluations: List[MockIndividualEvaluation]) -> Dict[str, Any]:
    """
    Simulate the Agent 4 scoring logic based on the actual implementation
    """
    # Calculate statistics
    total_questions = len(individual_evaluations)
    valid_responses = sum(1 for eval in individual_evaluations if eval.is_valid_response)
    dont_know_responses = sum(1 for eval in individual_evaluations if eval.is_dont_know_response)
    invalid_responses = total_questions - valid_responses
    
    # Count seniority distribution (only for valid responses)
    seniority_counts = {"senior": 0, "mid": 0, "junior": 0, "n/a": 0}
    for eval in individual_evaluations:
        if eval.is_valid_response:
            seniority_counts[eval.detected_seniority.value] += 1
        else:
            seniority_counts["n/a"] += 1
    
    # Calculate percentage based on all responses
    # Invalid responses count as 0 points and are included in denominator
    if total_questions > 0:
        percentage_score = (
            seniority_counts["senior"] * 100 +
            seniority_counts["mid"] * 75 +
            seniority_counts["junior"] * 50
        ) / total_questions  # Divide by all responses (including invalid ones)
    else:
        percentage_score = 0.0
    
    # Determine base seniority from individual evaluations (most common valid seniority)
    if valid_responses == 0:
        base_seniority = Seniority.NA
    else:
        # Find the most common seniority among valid responses
        valid_seniorities = [eval.detected_seniority for eval in individual_evaluations if eval.is_valid_response]
        if not valid_seniorities:
            base_seniority = Seniority.NA
        else:
            # Count occurrences of each seniority
            seniority_freq = {}
            for seniority in valid_seniorities:
                seniority_freq[seniority] = seniority_freq.get(seniority, 0) + 1
            
            # Get the most frequent seniority
            base_seniority = max(seniority_freq, key=seniority_freq.get)
    
    # Apply threshold downgrade logic (70% threshold)
    final_seniority = base_seniority
    threshold_applied = False
    
    if percentage_score < 70.0 and base_seniority != Seniority.NA:
        threshold_applied = True
        if base_seniority == Seniority.SENIOR:
            final_seniority = Seniority.MID
        elif base_seniority == Seniority.MID:
            final_seniority = Seniority.JUNIOR
        else:  # JUNIOR
            final_seniority = Seniority.JUNIOR
    
    return {
        "total_questions": total_questions,
        "valid_responses": valid_responses,
        "dont_know_responses": dont_know_responses,
        "invalid_responses": invalid_responses,
        "seniority_counts": seniority_counts,
        "percentage_score": round(percentage_score, 1),
        "base_seniority": base_seniority.value,
        "final_seniority": final_seniority.value,
        "threshold_applied": threshold_applied
    }

def analyze_provided_transcript():
    """
    Analyze the provided transcript data to understand the evaluation results
    """
    print("=== INTERVIEW EVALUATION ANALYSIS ===\n")
    
    # Based on the provided transcript, let's simulate the responses
    # The user mentioned "strong technical expertise and senior-level knowledge in 9 out of 11 valid responses"
    # But the final score was 45.0% with MID seniority
    
    # Let's create different scenarios to understand what could lead to 45.0%
    
    print("SCENARIO 1: User's reported case - 9 senior responses out of 11 valid, but 45.0% score")
    print("This suggests there were more than 11 total questions, with many invalid responses")
    
    # If 9 senior + 2 other valid responses = 11 valid responses
    # And final score is 45.0%, let's calculate total questions needed
    
    # Let's assume: 9 senior (900 points) + 2 mid (150 points) = 1050 points
    # For 45.0% score: 1050 / total_questions = 45.0
    # Therefore: total_questions = 1050 / 45.0 ≈ 23.33
    
    # Let's test with 20 total questions
    evaluations_scenario1 = []
    
    # 9 senior responses
    for i in range(9):
        evaluations_scenario1.append(MockIndividualEvaluation(i+1, True, False, Seniority.SENIOR))
    
    # 2 mid responses  
    for i in range(2):
        evaluations_scenario1.append(MockIndividualEvaluation(i+10, True, False, Seniority.MID))
    
    # 9 invalid responses (to make total 20)
    for i in range(9):
        evaluations_scenario1.append(MockIndividualEvaluation(i+12, False, False, Seniority.NA))
    
    result1 = simulate_agent4_scoring(evaluations_scenario1)
    print(f"Results: {json.dumps(result1, indent=2)}")
    print()
    
    print("SCENARIO 2: What if some 'I DON'T KNOW' responses were included?")
    
    # Based on the transcript, questions 18, 19, 20 had "I DON'T KNOW" responses
    evaluations_scenario2 = []
    
    # 9 senior responses
    for i in range(9):
        evaluations_scenario2.append(MockIndividualEvaluation(i+1, True, False, Seniority.SENIOR))
    
    # 2 mid responses
    for i in range(2):
        evaluations_scenario2.append(MockIndividualEvaluation(i+10, True, False, Seniority.MID))
    
    # 3 "I don't know" responses
    for i in range(3):
        evaluations_scenario2.append(MockIndividualEvaluation(i+12, False, True, Seniority.NA))
    
    # 6 other invalid responses
    for i in range(6):
        evaluations_scenario2.append(MockIndividualEvaluation(i+15, False, False, Seniority.NA))
    
    result2 = simulate_agent4_scoring(evaluations_scenario2)
    print(f"Results: {json.dumps(result2, indent=2)}")
    print()
    
    print("SCENARIO 3: Exact match to reported 45.0% score")

    # Let's work backwards from 45.0%
    # If we have 9 senior (900 points) + 2 mid (150 points) = 1050 points
    # For exactly 45.0%: total_questions = 1050 / 45.0 = 23.33
    # Let's try different combinations to get exactly 45.0%

    # Try: 9 senior + 0 mid + 2 junior = 900 + 0 + 100 = 1000 points
    # For 45.0%: 1000 / 45.0 = 22.22 questions
    # Let's try 22 questions: 1000 / 22 = 45.45%

    evaluations_scenario3 = []

    # 9 senior responses
    for i in range(9):
        evaluations_scenario3.append(MockIndividualEvaluation(i+1, True, False, Seniority.SENIOR))

    # 2 junior responses (instead of mid)
    for i in range(2):
        evaluations_scenario3.append(MockIndividualEvaluation(i+10, True, False, Seniority.JUNIOR))

    # 11 invalid responses (to make total 22)
    for i in range(11):
        evaluations_scenario3.append(MockIndividualEvaluation(i+12, False, False, Seniority.NA))

    result3 = simulate_agent4_scoring(evaluations_scenario3)
    print(f"Results: {json.dumps(result3, indent=2)}")
    print()

    print("SCENARIO 4: Testing the actual transcript data structure")
    print("Based on the provided transcript, there are 20 questions total")
    print("Questions 18, 19, 20 had 'I DON'T KNOW' responses")
    print("Let's simulate this exact scenario:")

    # Create evaluations based on the actual transcript
    evaluations_scenario4 = []

    # Questions 1-17: Mix of responses (9 senior-level, 2 mid-level, 6 other)
    senior_questions = [1, 2, 4, 5, 6, 10, 12, 13, 16]  # 9 questions
    mid_questions = [3, 11]  # 2 questions
    junior_questions = [7, 8, 9, 14, 15, 17]  # 6 questions

    for i in range(1, 18):  # Questions 1-17
        if i in senior_questions:
            evaluations_scenario4.append(MockIndividualEvaluation(i, True, False, Seniority.SENIOR))
        elif i in mid_questions:
            evaluations_scenario4.append(MockIndividualEvaluation(i, True, False, Seniority.MID))
        elif i in junior_questions:
            evaluations_scenario4.append(MockIndividualEvaluation(i, True, False, Seniority.JUNIOR))

    # Questions 18, 19, 20: "I DON'T KNOW" responses
    for i in range(18, 21):
        evaluations_scenario4.append(MockIndividualEvaluation(i, False, True, Seniority.NA))

    result4 = simulate_agent4_scoring(evaluations_scenario4)
    print(f"Results: {json.dumps(result4, indent=2)}")
    print()

    # Calculate what would give exactly 45.0%
    print("SCENARIO 5: Reverse engineering the exact 45.0% score")
    target_score = 45.0

    # If we have 9 senior responses, what combination gives us 45.0%?
    # The key insight: 9 senior responses = 9 * 100 = 900 points
    # For 45.0% score: 900 points / total_questions = 45.0
    # Therefore: total_questions = 900 / 45.0 = 20 questions

    print("Key insight: 9 senior responses (900 points) / 20 total questions = 45.0%")

    # Test this exact scenario
    evaluations_exact = []

    # 9 senior responses
    for i in range(9):
        evaluations_exact.append(MockIndividualEvaluation(i+1, True, False, Seniority.SENIOR))

    # 11 invalid responses (to make total 20)
    for i in range(11):
        evaluations_exact.append(MockIndividualEvaluation(i+10, False, False, Seniority.NA))

    result_exact = simulate_agent4_scoring(evaluations_exact)
    print(f"EXACT 45.0% scenario results: {json.dumps(result_exact, indent=2)}")
    print()

    print("CONCLUSION:")
    print("The 45.0% score with MID final seniority occurs when:")
    print("- 9 senior-level responses (900 points)")
    print("- 11 invalid/inadequate responses (0 points each)")
    print("- Total: 900 points / 20 questions = 45.0%")
    print("- Base seniority: SENIOR (most common among valid responses)")
    print("- Final seniority: MID (downgraded due to 45.0% < 70% threshold)")
    print()

    print("This explains the user's observation:")
    print("- 'Strong technical expertise and senior-level knowledge in 9 out of 11 valid responses'")
    print("- But the system counted 20 total questions, including 11 invalid ones")
    print("- The 70% threshold rule downgraded SENIOR → MID despite good valid responses")

if __name__ == "__main__":
    analyze_provided_transcript()
