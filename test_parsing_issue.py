#!/usr/bin/env python3
"""
Test script to demonstrate the Agent 1 parsing issue with the actual transcript format.
"""

def simulate_agent1_fallback_parsing(transcript):
    """
    Simulate the Agent 1 fallback parsing logic to identify the issue.
    """
    fallback_qa_pairs = []
    lines = transcript.split('\n')
    current_question = None
    current_answer = None
    question_num = 0
    in_expected_response = False
    
    print("=== PARSING SIMULATION ===")
    print(f"Total lines to process: {len(lines)}")
    print()
    
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
        
        print(f"Line {i+1}: '{line[:100]}{'...' if len(line) > 100 else ''}'")
        
        # Check for question patterns (numbered questions)
        if line and line[0].isdigit() and '.' in line:
            print(f"  -> DETECTED QUESTION: {line[:50]}...")
            
            # Save previous Q&A if exists
            if current_question and current_answer:
                question_num += 1
                print(f"  -> SAVING PREVIOUS Q&A #{question_num}")
                print(f"     Question: {current_question[:50]}...")
                print(f"     Answer: {current_answer[:50]}...")
                
                # Assess response quality
                answer_lower = current_answer.lower()
                if any(phrase in answer_lower for phrase in ['i may know', 'i cant remember', 'i dont really know', 'i don\'t know']):
                    quality = "inadequate"
                    valid = False
                elif 'interview was gonna be' in answer_lower or len(current_answer.strip()) < 5:
                    quality = "invalid"
                    valid = False
                elif len(current_answer.strip()) > 20 and not any(invalid in answer_lower for invalid in ['mmm', 'uhh']):
                    quality = "valid"
                    valid = True
                else:
                    quality = "inadequate"
                    valid = False
                
                print(f"     Quality: {quality}, Valid: {valid}")
                
                fallback_qa_pairs.append({
                    "question_number": question_num,
                    "question_text": current_question,
                    "answer_text": current_answer,
                    "has_valid_response": valid,
                    "response_quality": quality
                })
            
            current_question = line
            current_answer = ""
            in_expected_response = False
            print(f"  -> SET CURRENT QUESTION: {current_question[:50]}...")
        
        elif line.startswith('Expected Response:'):
            print(f"  -> FOUND 'Expected Response:' - SKIPPING")
            # In QA testing, "Expected Response:" contains the actual candidate's answer
            continue  # Skip the "Expected Response:" label, capture the content that follows
        
        elif current_question and line and not line.startswith('TECHNICAL SKILLS') and not line.startswith('SOFT SKILLS') and not line.startswith('METHODOLOGIES') and not line.startswith('LANGUAGE - TOOLS'):
            print(f"  -> ADDING TO ANSWER: {line[:50]}...")
            # This is the actual candidate response (could be after "Expected Response:" label)
            if not current_answer:  # First response line
                current_answer = line
            else:  # Additional response lines
                current_answer += " " + line
            print(f"  -> CURRENT ANSWER LENGTH: {len(current_answer)}")
        else:
            print(f"  -> SKIPPED (category or empty)")
    
    # Save last Q&A
    if current_question and current_answer:
        question_num += 1
        print(f"\n-> SAVING FINAL Q&A #{question_num}")
        print(f"   Question: {current_question[:50]}...")
        print(f"   Answer: {current_answer[:50]}...")
        
        # Assess response quality
        answer_lower = current_answer.lower()
        if any(phrase in answer_lower for phrase in ['i may know', 'i cant remember', 'i dont really know', 'i don\'t know']):
            quality = "inadequate"
            valid = False
        elif 'interview was gonna be' in answer_lower or len(current_answer.strip()) < 5:
            quality = "invalid"
            valid = False
        elif len(current_answer.strip()) > 20 and not any(invalid in answer_lower for invalid in ['mmm', 'uhh']):
            quality = "valid"
            valid = True
        else:
            quality = "inadequate"
            valid = False
        
        print(f"   Quality: {quality}, Valid: {valid}")
        
        fallback_qa_pairs.append({
            "question_number": question_num,
            "question_text": current_question,
            "answer_text": current_answer,
            "has_valid_response": valid,
            "response_quality": quality
        })
    
    print(f"\n=== PARSING RESULTS ===")
    print(f"Total Q&A pairs extracted: {len(fallback_qa_pairs)}")
    valid_count = sum(1 for qa in fallback_qa_pairs if qa["has_valid_response"])
    print(f"Valid responses: {valid_count}")
    print(f"Invalid responses: {len(fallback_qa_pairs) - valid_count}")
    
    print(f"\nDETAILED RESULTS:")
    for qa in fallback_qa_pairs:
        print(f"Q{qa['question_number']}: {qa['response_quality']} - {qa['answer_text'][:100]}...")
    
    return fallback_qa_pairs

def main():
    # Create the COMPLETE actual transcript format from the user
    sample_transcript = """1. Can you explain your experience with Snowflake and how you have utilized it in previous projects?
Expected Response:
I have architected multiple data solutions using Snowflake, focusing on performance optimization and scalability. My approach includes designing data models that leverage Snowflake's unique features, such as micro-partitioning and clustering, to enhance query performance. I have also integrated Snowflake with various ETL tools and data sources, ensuring seamless data ingestion and accessibility for stakeholders. Metrics such as reduced query times by 30% and improved data loading efficiency by 40% are key outcomes of my implementations.
TECHNICAL SKILLS
2. Describe your approach to schema modeling in data architecture.
Expected Response:
My approach to schema modeling involves a thorough analysis of business requirements and data relationships. I prioritize normalization and denormalization strategies based on use cases, ensuring that the schema supports both transactional and analytical workloads. I also implement best practices for indexing and partitioning to enhance performance. By aligning the schema design with business objectives, I have successfully reduced data retrieval times and improved overall system efficiency.
METHODOLOGIES
3. What methodologies do you follow when designing data pipelines?
Expected Response:
I employ a combination of Agile and DataOps methodologies to design data pipelines. This involves iterative development, continuous integration, and deployment practices to ensure that data products are delivered efficiently. I focus on automating data workflows using tools like Airflow, ensuring that data quality and lineage are maintained throughout the process. My experience includes leading cross-functional teams to align on requirements and deliver high-quality data solutions that meet business needs.
TECHNICAL SKILLS
4. How do you ensure data quality and integrity in your data architecture?
Expected Response:
I implement a comprehensive data governance framework that includes data validation, cleansing, and monitoring processes. This involves setting up automated checks and balances within the data pipelines to catch anomalies early. I also establish clear data lineage and documentation practices to ensure transparency and traceability. By aligning data quality metrics with business objectives, I have successfully improved data accuracy and reliability, leading to better decision-making.
TECHNICAL SKILLS
5. Can you discuss your experience with ETL tools and how you have used them in your projects?
Expected Response:
I have extensive experience with various ETL tools, including DBT and custom Python scripts, to design and implement robust data ingestion workflows. My focus has been on optimizing ETL processes for performance and reliability, ensuring that data is transformed and loaded efficiently into target systems. I have also integrated these tools with cloud platforms like AWS to enhance scalability and manageability. My projects have resulted in significant reductions in data processing times and improved data accessibility for stakeholders.
LANGUAGE - TOOLS
6. What role does Python play in your data architecture projects?
Expected Response:
Python is integral to my data architecture projects, primarily for scripting ETL processes and automating data workflows. I leverage libraries such as Pandas and NumPy for data manipulation and transformation, ensuring that data is processed efficiently. Additionally, I use Python for building custom data validation and monitoring scripts, which enhance data quality and integrity. My proficiency in Python has enabled me to streamline processes and improve overall system performance.
SOFT SKILLS
7. How do you approach cross-team communication in data projects?
Expected Response:
I prioritize establishing clear communication channels and fostering collaboration among cross-functional teams. I lead regular meetings to align on project goals, share updates, and address any challenges. My approach includes using collaborative tools to document decisions and track progress, ensuring that all stakeholders are informed and engaged. By promoting a culture of open communication, I have successfully navigated complex projects and delivered results that meet business objectives.
SOFT SKILLS
8. What strategies do you use to think creatively when solving data-related problems?
Expected Response:
I employ a structured approach to creative problem-solving, which includes brainstorming sessions and collaborative workshops with stakeholders. I encourage diverse perspectives and leverage design thinking principles to explore innovative solutions. My experience has taught me to balance creativity with practicality, ensuring that proposed solutions are feasible and aligned with business goals. This approach has led to the development of unique data products that drive significant business value.
SOFT SKILLS
9. How do you handle feedback and criticism in your work?
Expected Response:
I view feedback as an essential component of professional growth and actively seek it from peers and stakeholders. I approach criticism with an open mind, analyzing the input to identify areas for improvement. My experience has shown that incorporating feedback leads to better outcomes and fosters a culture of continuous improvement within teams. I also provide constructive feedback to others, promoting a collaborative environment.
LANGUAGE - TOOLS
10. What is your experience with data lineage tools, and how have you applied them in your projects?
Expected Response:
I have implemented data lineage tools such as Collibra and Atlan to enhance data governance and transparency in my projects. My approach includes mapping data flows and documenting transformations to provide stakeholders with a clear understanding of data origins and usage. This has been crucial in ensuring compliance with data regulations and improving trust in data quality. I have successfully reduced data discovery times by implementing these tools, enabling faster access to critical insights.
METHODOLOGIES
11. How do you ensure that your data architecture aligns with business needs?
Expected Response:
I engage with business stakeholders to understand their objectives and translate them into technical requirements. My approach includes conducting regular reviews and feedback sessions to ensure that the architecture evolves in line with changing business needs. I also establish key performance indicators (KPIs) to measure the effectiveness of the architecture in delivering business value. This alignment has resulted in data solutions that directly support strategic initiatives and drive measurable outcomes.
LANGUAGE - TOOLS
12. What is your experience with Airflow, and how have you used it in your data projects?
Expected Response:
I have extensive experience using Airflow to orchestrate complex data workflows and manage dependencies between tasks. My approach includes designing DAGs that optimize resource utilization and ensure timely execution of data pipelines. I have integrated Airflow with various data sources and destinations, enabling seamless data movement and transformation. My implementations have led to improved reliability and monitoring of data processes, significantly reducing downtime and manual intervention.
TECHNICAL SKILLS
13. How do you approach data security in your architecture designs?
Expected Response:
I prioritize data security by implementing a multi-layered approach that includes encryption, access controls, and regular audits. My experience involves collaborating with security teams to define policies and ensure compliance with regulations. I also incorporate best practices for data masking and anonymization to protect sensitive information. By aligning security measures with business objectives, I have successfully mitigated risks and ensured the integrity of data assets.
LANGUAGE - TOOLS
14. What is your experience with DataBricks, and how have you utilized it in your projects?
Expected Response:
I have leveraged DataBricks to build scalable data processing pipelines using Apache Spark. My experience includes designing and optimizing Spark jobs for performance, ensuring efficient data processing and transformation. I have integrated DataBricks with various data sources and utilized its collaborative features to enhance team productivity. My implementations have resulted in significant improvements in data processing times and the ability to handle large datasets effectively.
SOFT SKILLS
15. How do you manage stakeholder expectations in data projects?
Expected Response:
I proactively manage stakeholder expectations by establishing clear communication channels and setting realistic timelines. I engage stakeholders throughout the project lifecycle, providing regular updates and soliciting feedback to ensure alignment. My experience includes using project management tools to track progress and address any concerns promptly. By fostering transparency and collaboration, I have successfully delivered projects that meet or exceed stakeholder expectations.
TECHNICAL SKILLS
16. What techniques do you use for performance optimization in data systems?
Expected Response:
I employ a variety of techniques for performance optimization, including query tuning, indexing strategies, and data partitioning. My approach involves analyzing query performance metrics and identifying bottlenecks to implement targeted optimizations. I also leverage caching mechanisms and materialized views to enhance data retrieval speeds. By aligning performance improvements with business objectives, I have achieved significant reductions in query response times and improved overall system efficiency.
METHODOLOGIES
17. How do you approach documentation in your data architecture projects?
Expected Response:
I prioritize comprehensive documentation as a key component of successful data architecture projects. My approach includes creating detailed design documents, data dictionaries, and process flows that capture both technical and business aspects. I ensure that documentation is kept up-to-date and accessible to all stakeholders, facilitating knowledge transfer and onboarding. By promoting a culture of documentation, I have enhanced collaboration and reduced knowledge silos within teams.
LANGUAGE - TOOLS
18. What is your experience with AWS in relation to data architecture?
Expected Response:
I DON'T KNOW
METHODOLOGIES
19. How do you ensure that your data architecture is scalable?
Expected Response:
I DON'T KNOW.
LANGUAGE - TOOLS
20. What tools do you use for data visualization, and how do they fit into your data architecture?
Expected Response:
I DON'T KNOW"""
    
    print("Testing Agent 1 fallback parsing with actual transcript format...")
    print("="*80)
    
    result = simulate_agent1_fallback_parsing(sample_transcript)
    
    print("\n" + "="*80)
    print("ANALYSIS:")
    print("The parsing logic has issues with the transcript format!")
    print("- It correctly identifies questions (lines starting with numbers)")
    print("- It skips 'Expected Response:' lines")
    print("- But it may not correctly capture multi-line responses")
    print("- Category lines (TECHNICAL SKILLS, etc.) are being skipped correctly")
    print("- The issue might be in how it handles the response text after 'Expected Response:'")

if __name__ == "__main__":
    main()
