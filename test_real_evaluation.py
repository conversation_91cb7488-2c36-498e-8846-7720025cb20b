#!/usr/bin/env python3
"""
Test script to create a mock interview transcript and test the actual evaluation system.
This will help verify the scoring behavior with real data.
"""

import json
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_mock_transcript():
    """
    Create a mock interview transcript based on the provided data structure.
    This simulates the scenario that leads to 45.0% score with MID seniority.
    """
    
    # Create a transcript with 20 questions where:
    # - 9 questions have senior-level responses
    # - 11 questions have invalid/inadequate responses
    # This should result in exactly 45.0% score
    
    transcript_parts = []
    
    # Questions 1-9: Senior-level responses
    senior_responses = [
        "I have architected multiple data solutions using Snowflake, focusing on performance optimization and scalability. My approach includes designing data models that leverage Snowflake's unique features, such as micro-partitioning and clustering, to enhance query performance. I have also integrated Snowflake with various ETL tools and data sources, ensuring seamless data ingestion and accessibility for stakeholders. Metrics such as reduced query times by 30% and improved data loading efficiency by 40% are key outcomes of my implementations.",
        
        "My approach to schema modeling involves a thorough analysis of business requirements and data relationships. I prioritize normalization and denormalization strategies based on use cases, ensuring that the schema supports both transactional and analytical workloads. I also implement best practices for indexing and partitioning to enhance performance. By aligning the schema design with business objectives, I have successfully reduced data retrieval times and improved overall system efficiency.",
        
        "I employ a combination of Agile and DataOps methodologies to design data pipelines. This involves iterative development, continuous integration, and deployment practices to ensure that data products are delivered efficiently. I focus on automating data workflows using tools like Airflow, ensuring that data quality and lineage are maintained throughout the process. My experience includes leading cross-functional teams to align on requirements and deliver high-quality data solutions that meet business needs.",
        
        "I implement a comprehensive data governance framework that includes data validation, cleansing, and monitoring processes. This involves setting up automated checks and balances within the data pipelines to catch anomalies early. I also establish clear data lineage and documentation practices to ensure transparency and traceability. By aligning data quality metrics with business objectives, I have successfully improved data accuracy and reliability, leading to better decision-making.",
        
        "I have extensive experience with various ETL tools, including DBT and custom Python scripts, to design and implement robust data ingestion workflows. My focus has been on optimizing ETL processes for performance and reliability, ensuring that data is transformed and loaded efficiently into target systems. I have also integrated these tools with cloud platforms like AWS to enhance scalability and manageability. My projects have resulted in significant reductions in data processing times and improved data accessibility for stakeholders.",
        
        "Python is integral to my data architecture projects, primarily for scripting ETL processes and automating data workflows. I leverage libraries such as Pandas and NumPy for data manipulation and transformation, ensuring that data is processed efficiently. Additionally, I use Python for building custom data validation and monitoring scripts, which enhance data quality and integrity. My proficiency in Python has enabled me to streamline processes and improve overall system performance.",
        
        "I prioritize establishing clear communication channels and fostering collaboration among cross-functional teams. I lead regular meetings to align on project goals, share updates, and address any challenges. My approach includes using collaborative tools to document decisions and track progress, ensuring that all stakeholders are informed and engaged. By promoting a culture of open communication, I have successfully navigated complex projects and delivered results that meet business objectives.",
        
        "I employ a structured approach to creative problem-solving, which includes brainstorming sessions and collaborative workshops with stakeholders. I encourage diverse perspectives and leverage design thinking principles to explore innovative solutions. My experience has taught me to balance creativity with practicality, ensuring that proposed solutions are feasible and aligned with business goals. This approach has led to the development of unique data products that drive significant business value.",
        
        "I view feedback as an essential component of professional growth and actively seek it from peers and stakeholders. I approach criticism with an open mind, analyzing the input to identify areas for improvement. My experience has shown that incorporating feedback leads to better outcomes and fosters a culture of continuous improvement within teams. I also provide constructive feedback to others, promoting a collaborative environment."
    ]
    
    # Add senior responses (questions 1-9)
    for i, response in enumerate(senior_responses, 1):
        transcript_parts.append(f"Question {i}: Can you explain your experience with data architecture and how you have utilized it in previous projects?")
        transcript_parts.append(f"Answer {i}: {response}")
        transcript_parts.append("")
    
    # Questions 10-20: Invalid/inadequate responses
    invalid_responses = [
        "I DON'T KNOW",
        "I DON'T KNOW", 
        "I DON'T KNOW",
        "mmmmmm",
        "uhhhh",
        "Not sure about that",
        "I haven't worked with that",
        "...",
        "I'm not familiar with that technology",
        "I don't have experience with that",
        "I'm not sure how to answer that"
    ]
    
    # Add invalid responses (questions 10-20)
    for i, response in enumerate(invalid_responses, 10):
        transcript_parts.append(f"Question {i}: Technical question about advanced concepts?")
        transcript_parts.append(f"Answer {i}: {response}")
        transcript_parts.append("")
    
    return "\n".join(transcript_parts)

def create_mock_questions():
    """
    Create mock interview questions with expected responses for different seniority levels.
    """
    questions = []
    
    # Create 20 questions with expected responses
    for i in range(1, 21):
        question = {
            "question_number": i,
            "question_text": f"Question {i}: Technical question about data architecture concepts?",
            "expected_response": {
                "junior_answer": "Basic understanding of the concept with minimal detail.",
                "mid_answer": "Good understanding with some practical examples and moderate depth.",
                "senior_answer": "Deep expertise with comprehensive knowledge, advanced concepts, and specific metrics or outcomes."
            }
        }
        questions.append(question)
    
    return {"questions": questions}

def main():
    """
    Main function to create test data and display the expected evaluation results.
    """
    print("=== MOCK INTERVIEW EVALUATION TEST ===\n")
    
    # Create mock data
    transcript = create_mock_transcript()
    questions = create_mock_questions()
    
    print("Created mock interview with:")
    print("- 20 total questions")
    print("- 9 senior-level responses (questions 1-9)")
    print("- 11 invalid/inadequate responses (questions 10-20)")
    print()
    
    print("Expected evaluation results:")
    print("- Total questions: 20")
    print("- Valid responses: 9 (all senior-level)")
    print("- Invalid responses: 11")
    print("- Points calculation: 9 × 100 = 900 points")
    print("- Percentage score: 900 ÷ 20 = 45.0%")
    print("- Base seniority: SENIOR (most common among valid responses)")
    print("- Final seniority: MID (downgraded due to 45.0% < 70% threshold)")
    print()
    
    print("This matches the user's reported case:")
    print("- 'Strong technical expertise and senior-level knowledge in 9 out of 11 valid responses'")
    print("- Final score: 45.0%")
    print("- Final seniority: MID")
    print()
    
    # Save the mock data for potential testing
    with open("mock_transcript.txt", "w", encoding="utf-8") as f:
        f.write(transcript)
    
    with open("mock_questions.json", "w", encoding="utf-8") as f:
        json.dump(questions, f, indent=2, ensure_ascii=False)
    
    print("Mock data saved to:")
    print("- mock_transcript.txt")
    print("- mock_questions.json")
    print()
    
    print("ANALYSIS SUMMARY:")
    print("================")
    print("The 45.0% score with MID seniority is working as designed:")
    print()
    print("1. SCORING ALGORITHM:")
    print("   - Senior responses: 100 points each")
    print("   - Mid responses: 75 points each") 
    print("   - Junior responses: 50 points each")
    print("   - Invalid responses: 0 points each")
    print("   - Total score = sum of points ÷ total questions")
    print()
    print("2. SENIORITY DETERMINATION:")
    print("   - Base seniority = most common level among valid responses")
    print("   - If score < 70%, seniority is downgraded one level")
    print("   - Senior → Mid, Mid → Junior, Junior stays Junior")
    print()
    print("3. THE ISSUE:")
    print("   - The system counts ALL questions (including invalid ones) in the denominator")
    print("   - This heavily penalizes candidates who don't answer some questions")
    print("   - Even with 9/9 senior responses, 11 invalid responses drag the score to 45%")
    print()
    print("4. POTENTIAL IMPROVEMENTS:")
    print("   - Consider excluding invalid responses from the denominator")
    print("   - Or use a different weighting system for incomplete responses")
    print("   - Or adjust the 70% threshold based on response completion rate")

if __name__ == "__main__":
    main()
